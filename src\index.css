@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Optimized for 100% zoom - Base styles */
@layer base {
  html {
    font-size: 14px; /* Slightly smaller base font for better 100% zoom experience */
  }

  body {
    font-size: 0.875rem; /* 14px equivalent */
    line-height: 1.4;
  }
}

/* Custom scrollbar styles for component palette */
.component-palette-scroll::-webkit-scrollbar {
  width: 6px;
}

.component-palette-scroll::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.component-palette-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 3px;
}

.component-palette-scroll::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Smooth animations */
.component-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.component-card:hover {
  transform: translateY(-2px) scale(1.02);
}

/* Compact layout utilities for 100% zoom */
@layer utilities {
  .compact-spacing {
    @apply space-y-2;
  }

  .compact-padding {
    @apply p-2;
  }

  .compact-text {
    @apply text-xs;
  }

  .compact-button {
    @apply px-2 py-1 text-xs;
  }

  .compact-icon {
    @apply w-3 h-3;
  }
}

/* Enhanced gradient backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-border {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1px;
  border-radius: 12px;
}

.gradient-border-inner {
  background: white;
  border-radius: 11px;
}

/* Production-grade hero animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Optimized animations with hardware acceleration */
@keyframes float {
  0%, 100% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, -10px, 0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(108, 99, 255, 0.3);
    transform: scale3d(1, 1, 1);
  }
  50% {
    box-shadow: 0 0 30px rgba(108, 99, 255, 0.6);
    transform: scale3d(1.02, 1.02, 1);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Smooth fade animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Optimized hero text animations */
.hero-text-animate {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  will-change: transform, opacity;
}

.hero-text-stagger-1 {
  animation: fadeInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s forwards;
  opacity: 0;
  will-change: transform, opacity;
}

.hero-text-stagger-2 {
  animation: fadeInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
  opacity: 0;
  will-change: transform, opacity;
}

.hero-subtitle-animate {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
  opacity: 0;
  will-change: transform, opacity;
}

/* Optimized button animations */
.btn-shimmer {
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.btn-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translateX(0);
  will-change: transform;
}

.btn-shimmer:hover::before {
  transform: translateX(200%);
}

/* Optimized floating elements */
.float-animation {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
}

.float-delay-1 {
  animation-delay: 2s;
  will-change: transform;
}

.float-delay-2 {
  animation-delay: 4s;
  will-change: transform;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-transition {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* High-quality image rendering */
.high-quality-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Gradient text */
.text-gradient {
  background: linear-gradient(135deg, #6C63FF 0%, #8B7CF6 50%, #A855F7 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Smooth transitions */
.smooth-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glass effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
