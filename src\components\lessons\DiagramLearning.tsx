import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import jsPDF from 'jspdf';
import {
  X,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  BookOpen,
  ExternalLink,
  Play,
  FileText,
  Video,
  Globe,
  ChevronRight,
  Info,
  Sparkles,
  Map
} from 'lucide-react';
import { kubernetesArchitectureData } from '../../data/kubernetesArchitecture';
import { DiagramCategory, DiagramComponent } from '../../data/lessons';

interface DiagramLearningProps {
  onClose: () => void;
}

const DiagramLearning: React.FC<DiagramLearningProps> = ({ onClose }) => {
  const [selectedCategory, setSelectedCategory] = useState<DiagramCategory | null>(null);
  const [selectedComponent, setSelectedComponent] = useState<DiagramComponent | null>(null);
  const [hoveredComponent, setHoveredComponent] = useState<DiagramComponent | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [showConnections, setShowConnections] = useState(true);
  const [animationPhase, setAnimationPhase] = useState(0);

  // Auto-animate introduction
  useEffect(() => {
    const timer = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  const handleCategoryClick = (category: DiagramCategory) => {
    setSelectedCategory(category);
    setSelectedComponent(null);
  };

  const handleComponentClick = (component: DiagramComponent) => {
    setSelectedComponent(component);
  };

  const handleZoomIn = () => setZoomLevel(prev => Math.min(prev + 0.2, 2));
  const handleZoomOut = () => setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  const handleReset = () => {
    setZoomLevel(1);
    setSelectedCategory(null);
    setSelectedComponent(null);
  };

  const exportToPDF = () => {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    let yPosition = 20;

    // Title
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    pdf.text('Kubernetes Architecture Learning Map', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 15;

    // Subtitle
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.text('Interactive learning guide for Kubernetes components and concepts', pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 20;

    // Categories and Components
    kubernetesArchitectureData.forEach((category) => {
      // Check if we need a new page
      if (yPosition > pageHeight - 40) {
        pdf.addPage();
        yPosition = 20;
      }

      // Category Header
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      pdf.text(category.name, 20, yPosition);
      yPosition += 8;

      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Difficulty: ${category.difficulty} | Estimated Time: ${category.estimatedTime}`, 20, yPosition);
      yPosition += 6;

      pdf.text(category.description, 20, yPosition, { maxWidth: pageWidth - 40 });
      yPosition += 12;

      // Components
      category.components.forEach((component) => {
        // Check if we need a new page
        if (yPosition > pageHeight - 30) {
          pdf.addPage();
          yPosition = 20;
        }

        // Component Name
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.text(`${component.icon} ${component.name}`, 30, yPosition);
        yPosition += 6;

        // Component Description
        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'normal');
        pdf.text(component.detailedDescription, 30, yPosition, { maxWidth: pageWidth - 60 });
        yPosition += 8;

        // Resources
        if (component.resources.length > 0) {
          pdf.setFontSize(9);
          pdf.setFont('helvetica', 'bold');
          pdf.text('Resources:', 30, yPosition);
          yPosition += 4;

          component.resources.forEach((resource) => {
            pdf.setFont('helvetica', 'normal');
            pdf.text(`• ${resource.title} (${resource.type})`, 35, yPosition);
            yPosition += 4;
            if (resource.description) {
              pdf.setFontSize(8);
              pdf.text(`  ${resource.description}`, 37, yPosition);
              yPosition += 3;
              pdf.setFontSize(9);
            }
          });
        }

        yPosition += 8;
      });

      yPosition += 5;
    });

    // Footer
    const totalPages = pdf.internal.pages.length - 1;
    for (let i = 1; i <= totalPages; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.text(`Generated by KubeQuest - Page ${i} of ${totalPages}`, pageWidth / 2, pageHeight - 10, { align: 'center' });
    }

    // Save the PDF
    pdf.save('kubernetes-architecture-learning-map.pdf');
  };

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'docs': return <FileText className="w-4 h-4" />;
      case 'video': return <Video className="w-4 h-4" />;
      case 'article': return <Globe className="w-4 h-4" />;
      default: return <ExternalLink className="w-4 h-4" />;
    }
  };

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'architecture-components': return '🏗️';
      case 'core-objects': return '🧱';
      case 'controllers': return '⚙️';
      case 'observability': return '👁️';
      case 'security': return '🔒';
      case 'extensibility': return '🔧';
      default: return '📦';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl h-full max-h-[90vh] flex flex-col overflow-hidden mx-auto"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 md:p-6 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 md:w-10 md:h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
              <Map className="w-4 h-4 md:w-6 md:h-6" />
            </div>
            <div>
              <h2 className="text-lg md:text-2xl font-bold">Kubernetes Architecture Map</h2>
              <p className="text-blue-100 text-sm md:text-base hidden sm:block">Interactive learning through visual exploration</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={exportToPDF}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 px-2 md:px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              <span className="hidden md:inline">Export PDF</span>
            </button>
            <button
              onClick={onClose}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 p-2 rounded-lg transition-colors"
            >
              <X className="w-4 h-4 md:w-5 md:h-5" />
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Main Diagram Area */}
          <div className="flex-1 relative bg-gradient-to-br from-slate-50 to-blue-50 overflow-auto">
            {/* Controls */}
            <div className="absolute top-4 left-4 z-20 flex flex-col gap-2">
              <div className="bg-white rounded-lg shadow-lg p-2 flex gap-1">
                <button
                  onClick={handleZoomIn}
                  className="p-2 hover:bg-gray-100 rounded transition-colors"
                  title="Zoom In"
                >
                  <ZoomIn className="w-4 h-4" />
                </button>
                <button
                  onClick={handleZoomOut}
                  className="p-2 hover:bg-gray-100 rounded transition-colors"
                  title="Zoom Out"
                >
                  <ZoomOut className="w-4 h-4" />
                </button>
                <button
                  onClick={handleReset}
                  className="p-2 hover:bg-gray-100 rounded transition-colors"
                  title="Reset View"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Simplified Grid Layout */}
            <div className="p-8 min-h-full">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                {kubernetesArchitectureData.map((category, categoryIndex) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: categoryIndex * 0.1 }}
                    className="relative"
                  >
                    {/* Category Card */}
                    <motion.div
                      className={`bg-gradient-to-br ${category.color} rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer ${
                        selectedCategory?.id === category.id ? 'ring-4 ring-blue-300 shadow-2xl scale-105' : 'hover:scale-105'
                      }`}
                      onClick={() => handleCategoryClick(category)}
                      whileHover={{ y: -5 }}
                    >
                      <div className="text-white text-center">
                        <motion.div
                          className="text-3xl mb-3"
                          animate={{
                            rotate: animationPhase === categoryIndex ? [0, 10, -10, 0] : 0
                          }}
                          transition={{ duration: 1 }}
                        >
                          {getCategoryIcon(category.id)}
                        </motion.div>
                        <h3 className="font-bold text-xl mb-2">{category.name}</h3>
                        <p className="text-sm opacity-90 mb-3">{category.description}</p>
                        <div className="flex items-center justify-center gap-4 text-sm">
                          <div className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                            {category.estimatedTime}
                          </div>
                          <div className="bg-white bg-opacity-20 px-3 py-1 rounded-full flex items-center gap-1">
                            <Sparkles className="w-3 h-3" />
                            {category.difficulty}
                          </div>
                        </div>
                      </div>
                    </motion.div>

                    {/* Components Grid (shown when category is selected) */}
                    <AnimatePresence>
                      {selectedCategory?.id === category.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-4 overflow-hidden"
                        >
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {category.components.map((component, componentIndex) => (
                              <motion.div
                                key={component.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: componentIndex * 0.1 }}
                                className={`${component.color} text-white rounded-lg p-4 shadow-md hover:shadow-lg cursor-pointer transition-all duration-200 transform hover:scale-105 relative group`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleComponentClick(component);
                                }}
                                onMouseEnter={() => setHoveredComponent(component)}
                                onMouseLeave={() => setHoveredComponent(null)}
                              >
                                <div className="text-center">
                                  <div className="text-2xl mb-2">{component.icon}</div>
                                  <h4 className="font-semibold text-sm mb-1">{component.name}</h4>
                                  <p className="text-xs opacity-90">{component.description}</p>
                                  <div className="mt-2 text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                                    {component.difficulty}
                                  </div>
                                </div>

                                {/* Hover Tooltip */}
                                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-30">
                                  <div className="bg-gray-900 text-white p-3 rounded-lg shadow-xl max-w-xs text-center">
                                    <h5 className="font-bold text-sm mb-1">{component.name}</h5>
                                    <p className="text-xs text-gray-300">{component.detailedDescription.substring(0, 100)}...</p>
                                    <div className="mt-2 flex items-center justify-center gap-1 text-xs">
                                      <Info className="w-3 h-3" />
                                      <span>Click for full details</span>
                                    </div>
                                    {/* Arrow */}
                                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                                  </div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>

          {/* Side Panel */}
          <AnimatePresence>
            {selectedComponent && (
              <motion.div
                initial={{ x: 400, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                exit={{ x: 400, opacity: 0 }}
                className="w-full md:w-96 bg-white border-l border-gray-200 flex flex-col absolute md:relative inset-0 md:inset-auto z-30"
              >
                {/* Component Details Header */}
                <div className={`${selectedComponent.color} text-white p-6`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-3xl">{selectedComponent.icon}</div>
                    <button
                      onClick={() => setSelectedComponent(null)}
                      className="text-white hover:bg-white hover:bg-opacity-20 p-1 rounded"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  <h3 className="text-xl font-bold mb-2">{selectedComponent.name}</h3>
                  <p className="text-sm opacity-90">{selectedComponent.description}</p>
                  <div className="mt-3 flex items-center gap-4 text-sm">
                    <span className="bg-white bg-opacity-20 px-2 py-1 rounded">
                      {selectedComponent.difficulty}
                    </span>
                    <span className="bg-white bg-opacity-20 px-2 py-1 rounded">
                      {selectedComponent.category}
                    </span>
                  </div>
                </div>

                {/* Component Details Content */}
                <div className="flex-1 overflow-y-auto p-6">
                  {/* Detailed Description */}
                  <div className="mb-6">
                    <h4 className="font-bold text-gray-900 mb-3 flex items-center gap-2">
                      <BookOpen className="w-4 h-4" />
                      Detailed Overview
                    </h4>
                    <p className="text-gray-700 text-sm leading-relaxed">
                      {selectedComponent.detailedDescription}
                    </p>
                  </div>

                  {/* Learning Resources */}
                  <div className="mb-6">
                    <h4 className="font-bold text-gray-900 mb-3 flex items-center gap-2">
                      <ExternalLink className="w-4 h-4" />
                      Learning Resources
                    </h4>
                    <div className="space-y-3">
                      {selectedComponent.resources.map((resource, index) => (
                        <motion.a
                          key={index}
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                        >
                          <div className="text-blue-600">
                            {getResourceIcon(resource.type)}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm text-gray-900 group-hover:text-blue-600 transition-colors">
                              {resource.title}
                            </div>
                            {resource.description && (
                              <div className="text-xs text-gray-600 mt-1">
                                {resource.description}
                              </div>
                            )}
                          </div>
                          <ChevronRight className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors" />
                        </motion.a>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <h4 className="font-bold text-gray-900 mb-3">Tags</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedComponent.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default DiagramLearning;
