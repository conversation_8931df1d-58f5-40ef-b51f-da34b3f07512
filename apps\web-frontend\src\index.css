@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-slate-200 rounded;
}

/* Interactive elements */
.interactive-hover {
  @apply transition-all duration-200 hover:scale-105 hover:shadow-lg;
}

/* Code blocks */
.code-block {
  @apply bg-slate-900 text-slate-100 p-4 rounded-lg font-mono text-sm overflow-x-auto;
}

/* Responsive text */
@layer utilities {
  .text-responsive-xs { @apply text-xs sm:text-sm; }
  .text-responsive-sm { @apply text-sm sm:text-base; }
  .text-responsive-base { @apply text-base sm:text-lg; }
  .text-responsive-lg { @apply text-lg sm:text-xl; }
  .text-responsive-xl { @apply text-xl sm:text-2xl; }
  .text-responsive-2xl { @apply text-2xl sm:text-3xl; }
  .text-responsive-3xl { @apply text-3xl sm:text-4xl; }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
