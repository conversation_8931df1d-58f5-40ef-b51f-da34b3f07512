{"name": "shared-ui", "version": "1.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "typescript": "^5.5.3", "eslint": "^9.9.1"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}}