# Cloud Architecture Learning Scenarios

This document contains detailed, robust scenarios for a cloud learning application. Each scenario includes a description, user prompt, services involved, metadata, a visual architecture description, and complete Terraform code.

---

##  Scenario 1: Static Website Hosting on AWS ☁️

* **Title**: Static Website Hosting on AWS
* **Description**: Learn the fundamentals of hosting secure, fast, and scalable static websites on AWS with global CDN distribution for optimal performance.
* **User Prompt**: "Build me a simple architecture to host a static website on AWS."
* **Services Involved**:
    * S3 (Simple Storage Service)
    * CloudFront (Content Delivery Network)
    * Route 53 (DNS Service)
    * IAM (Identity and Access Management)
    * ACM (AWS Certificate Manager)
* **Level**: Beginner
* **Estimated Time**: 15 minutes
* **Cost Level**: Low
* **Export Formats**: PNG, Terraform

### **Architecture Diagram (PNG) Description**

The diagram shows a user on the left connecting via the Internet. The request first hits **Route 53**, which holds the custom domain name (e.g., `www.your-site.com`). Route 53 directs the traffic to a **CloudFront Distribution**. An arrow points from Route 53 to CloudFront. The CloudFront distribution is shown as a global network of edge locations. An **ACM Certificate** is attached to the CloudFront distribution, indicated by a lock icon, enabling HTTPS. The CloudFront distribution's origin points to an **S3 Bucket**, which is configured for static website hosting. An arrow points from CloudFront to the S3 bucket. The S3 bucket icon contains files like `index.html` and `error.html`. A dotted line box around the S3 bucket and CloudFront indicates that CloudFront is caching content from S3.

### **Terraform Code (`main.tf`)**

```terraform
# main.tf
# This Terraform code deploys a secure, CDN-backed static website on AWS.

# Configure the AWS provider
provider "aws" {
  region = "us-east-1" # CloudFront requires ACM certificate in us-east-1
}

variable "domain_name" {
  description = "The custom domain name for the website, e.g., my-awesome-site.com"
  type        = string
}

# Find the Route 53 hosted zone for your domain
data "aws_route53_zone" "primary" {
  name         = var.domain_name
  private_zone = false
}

# Create an S3 bucket to store the website files.
# The bucket is private and only accessible by CloudFront.
resource "aws_s3_bucket" "site_bucket" {
  bucket = var.domain_name
  # It's best practice to not enable public access on the bucket itself.
}

# Create a CloudFront Origin Access Identity (OAI)
# This allows CloudFront to securely access the private S3 bucket.
resource "aws_cloudfront_origin_access_identity" "oai" {
  comment = "OAI for ${var.domain_name}"
}

# Create a bucket policy that grants the OAI read access.
resource "aws_s3_bucket_policy" "bucket_policy" {
  bucket = aws_s3_bucket.site_bucket.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect    = "Allow",
        Principal = {
          AWS = aws_cloudfront_origin_access_identity.oai.iam_arn
        },
        Action   = "s3:GetObject",
        Resource = "${aws_s3_bucket.site_bucket.arn}/*"
      }
    ]
  })
}

# Provision an SSL certificate for the custom domain using ACM.
resource "aws_acm_certificate" "cert" {
  domain_name       = var.domain_name
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}

# Create the DNS records needed to validate the ACM certificate.
resource "aws_route53_record" "cert_validation" {
  for_each = {
    for dvo in aws_acm_certificate.cert.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.primary.zone_id
}

# Wait for the certificate to be validated before using it.
resource "aws_acm_certificate_validation" "cert" {
  certificate_arn         = aws_acm_certificate.cert.arn
  validation_record_fqdns = [for record in aws_route53_record.cert_validation : record.fqdn]
}

# Create the CloudFront distribution.
resource "aws_cloudfront_distribution" "s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.site_bucket.bucket_regional_domain_name
    origin_id   = "S3-${var.domain_name}"

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.oai.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"
  aliases             = [var.domain_name]

  default_cache_behavior {
    allowed_methods  = ["GET", "HEAD", "OPTIONS"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = "S3-${var.domain_name}"

    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }

    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
  }

  # Redirect HTTP to HTTPS
  viewer_certificate {
    acm_certificate_arn      = aws_acm_certificate_validation.cert.certificate_arn
    ssl_support_method       = "sni-only"
    minimum_protocol_version = "TLSv1.2_2021"
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }
}

# Create the Route 53 A record to point the domain to the CloudFront distribution.
resource "aws_route53_record" "site_dns" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = var.domain_name
  type    = "A"

  alias {
    name                   = aws_cloudfront_distribution.s3_distribution.domain_name
    zone_id                = aws_cloudfront_distribution.s3_distribution.hosted_zone_id
    evaluate_target_health = false
  }
}

# Output the website URL
output "website_url" {
  value = "https://${var.domain_name}"
}
Scenario 2: Scalable Serverless API on Azure 💠
Title: Scalable Serverless API on Azure

Description: Build a highly scalable, event-driven, and cost-efficient REST API backend using Azure's core serverless components.

User Prompt: "Build a scalable, low-cost API for my mobile app on Azure."

Services Involved:

Azure Functions (for compute)

API Management (for gateway, security, and policies)

Cosmos DB (for NoSQL database)

Application Insights (for monitoring)

Level: Intermediate

Estimated Time: 25 minutes

Cost Level: Low

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows a Mobile App/Client on the far left. An arrow points from the client to an API Management instance, which acts as the API gateway. The API Management icon has a smaller key icon on it, representing authentication (e.g., subscription keys). The gateway defines the API routes (e.g., /api/items). An arrow points from API Management to an Azure Function App. The Function App contains multiple HTTP-triggered functions (e.g., CreateItem, GetItem). An arrow points from the Function App to a Cosmos DB (NoSQL API) instance, representing data storage and retrieval. A separate Application Insights service is shown, with dotted lines pointing to both the API Management and the Function App, indicating it's collecting logs and telemetry from both.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform code deploys a serverless API with Azure Functions and Cosmos DB, fronted by API Management.

# Configure the Azure provider
provider "azurerm" {
  features {}
}

variable "resource_group_name" {
  description = "The name of the resource group."
  type        = string
  default     = "rg-serverless-api-demo"
}

variable "location" {
  description = "The Azure region to deploy resources."
  type        = string
  default     = "East US"
}

variable "app_name_prefix" {
  description = "A unique prefix for naming resources."
  type        = string
}

# Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = var.resource_group_name
  location = var.location
}

# Create an Application Insights instance for monitoring
resource "azurerm_application_insights" "app_insights" {
  name                = "${var.app_name_prefix}-appinsights"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  application_type    = "web"
}

# Create a storage account required by the Function App
resource "azurerm_storage_account" "storage" {
  name                     = "${var.app_name_prefix}funcsa"
  resource_group_name      = azurerm_resource_group.rg.name
  location                 = azurerm_resource_group.rg.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
}

# Create a Cosmos DB account (NoSQL API)
resource "azurerm_cosmosdb_account" "db" {
  name                = "${var.app_name_prefix}-cosmosdb"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  offer_type          = "Standard"
  kind                = "GlobalDocumentDB"

  consistency_policy {
    consistency_level = "Session"
  }

  geo_location {
    location          = azurerm_resource_group.rg.location
    failover_priority = 0
  }
}

# Create a Cosmos DB SQL database
resource "azurerm_cosmosdb_sql_database" "db" {
  name                = "ItemsDB"
  resource_group_name = azurerm_cosmosdb_account.db.resource_group_name
  account_name        = azurerm_cosmosdb_account.db.name
}

# Create a Cosmos DB SQL container
resource "azurerm_cosmosdb_sql_container" "container" {
  name                  = "Items"
  resource_group_name   = azurerm_cosmosdb_account.db.resource_group_name
  account_name          = azurerm_cosmosdb_account.db.name
  database_name         = azurerm_cosmosdb_sql_database.db.name
  partition_key_path    = "/id"
  throughput            = 400
}


# Create a consumption plan for the Function App
resource "azurerm_service_plan" "plan" {
  name                = "${var.app_name_prefix}-plan"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  os_type             = "Linux"
  sku_name            = "Y1" # Consumption plan
}

# Create the Linux Function App
resource "azurerm_linux_function_app" "function_app" {
  name                = "${var.app_name_prefix}-functions"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location

  storage_account_name       = azurerm_storage_account.storage.name
  storage_account_access_key = azurerm_storage_account.storage.primary_access_key
  service_plan_id            = azurerm_service_plan.plan.id

  site_config {
    application_stack {
      # Assuming Node.js for the functions
      node_version = "~18"
    }
  }

  app_settings = {
    "FUNCTIONS_WORKER_RUNTIME"      = "node"
    "AzureWebJobsStorage"           = azurerm_storage_account.storage.primary_connection_string
    "CosmosDBConnection"            = azurerm_cosmosdb_account.db.connection_strings[0]
    "APPINSIGHTS_INSTRUMENTATIONKEY"= azurerm_application_insights.app_insights.instrumentation_key
  }
}

# Create an API Management service
resource "azurerm_api_management" "apim" {
  name                = "${var.app_name_prefix}-apim"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  publisher_name      = "My Company"
  publisher_email     = "<EMAIL>"

  sku_name = "Consumption_0"
}

# Output the API Management gateway URL
output "api_gateway_url" {
  value = azurerm_api_management.apim.gateway_url
}
Scenario 3: Containerized Web App on GCP Cloud Run 🌐
Title: Containerized Web App on GCP Cloud Run

Description: Deploy a containerized application as a fully managed, auto-scaling serverless web service on Google Cloud Run.

User Prompt: "Deploy my Node.js Docker container as a scalable web service on GCP."

Services Involved:

Cloud Run (Serverless container runtime)

Artifact Registry (Container image storage)

Cloud Build (Automated container builds)

IAM (Identity and Access Management)

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram begins with a Developer pushing code to a GitHub/Cloud Source Repository. This action triggers Cloud Build. An arrow points from the code repository to Cloud Build. Cloud Build is shown as a pipeline with steps: Build and Push. The Build step takes the source code (including a Dockerfile) and creates a Docker image. An arrow points from the Build step to the Push step. The Push step pushes the container image into Artifact Registry. An arrow points from Cloud Build to Artifact Registry. Finally, a Cloud Run service is shown, configured to pull its image from the Artifact Registry. An arrow points from Artifact Registry to Cloud Run. The Cloud Run service automatically scales from zero to N instances and is exposed to the internet via a public URL. A User icon points to the Cloud Run service, representing public access.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform code sets up the infrastructure to deploy a container to Cloud Run.
# NOTE: This assumes you have a Docker image already pushed to Artifact Registry.
# A full CI/CD pipeline would also include the Cloud Build trigger resource.

# Configure the Google Cloud provider
provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "service_name" {
  description = "The name for the Cloud Run service."
  type        = string
  default     = "my-web-app"
}

variable "location" {
  description = "The GCP region for the service."
  type        = string
  default     = "us-central1"
}

variable "container_image" {
  description = "The full path to the container image in Artifact Registry."
  type        = string
  # Example: "us-central1-docker.pkg.dev/your-gcp-project-id/my-repo/my-image:latest"
}

# Enable necessary Google Cloud APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "run.googleapis.com",
    "iam.googleapis.com",
    "cloudbuild.googleapis.com",
    "artifactregistry.googleapis.com"
  ])
  service            = each.key
  disable_on_destroy = false
}

# Create the Cloud Run service
resource "google_cloud_run_v2_service" "default" {
  name     = var.service_name
  location = var.location

  # This defines the container to be deployed
  template {
    containers {
      image = var.container_image
      ports {
        container_port = 8080 # Assuming the container listens on port 8080
      }
    }
  }

  # Configure traffic to send 100% to the latest revision
  traffic {
    type    = "TRAFFIC_TARGET_ALLOCATION_TYPE_LATEST"
    percent = 100
  }

  depends_on = [
    google_project_service.apis,
  ]
}

# Create an IAM policy to allow unauthenticated (public) access to the service.
# For internal services, you would configure authenticated invocations instead.
resource "google_cloud_run_v2_service_iam_member" "noauth" {
  location = google_cloud_run_v2_service.default.location
  name     = google_cloud_run_v2_service.default.name
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Output the URL of the deployed Cloud Run service
output "service_url" {
  description = "The URL of the deployed Cloud Run service."
  value       = google_cloud_run_v2_service.default.uri
}

_-----
Scenario 4: Real-time Data Processing on AWS 📈
Title: Real-time Data Processing Pipeline

Description: Build an end-to-end, serverless pipeline to ingest, process, and store real-time streaming data on AWS.

User Prompt: "Create a system to analyze real-time clickstream data from my website."

Services Involved:

Kinesis Data Streams (for data ingestion)

Lambda (for data processing)

S3 (for processed data storage/data lake)

IAM (for permissions)

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Medium

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows data producers (like web or mobile clients) on the left sending a continuous stream of data into an Amazon Kinesis Data Stream. An arrow points from the producers to the Kinesis stream icon. A Lambda function is configured as a consumer of this stream, indicated by a trigger icon connecting Kinesis to Lambda. The Lambda function reads batches of records from the stream, processes them (e.g., transforms JSON, enriches data), and then writes the processed data into an S3 Bucket, which is labeled "Processed Data Lake". An arrow points from the Lambda function to the S3 bucket. All resources are governed by an IAM Role that grants the Lambda function the necessary permissions to read from Kinesis and write to S3.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform code deploys a serverless data processing pipeline on AWS.

provider "aws" {
  region = "us-east-1"
}

variable "stream_name" {
  description = "The name for the Kinesis Data Stream."
  default     = "real-time-data-stream"
}

variable "s3_bucket_name" {
  description = "A unique name for the S3 bucket to store processed data."
}

# 1. Create the Kinesis Data Stream for ingesting real-time data
resource "aws_kinesis_stream" "data_stream" {
  name        = var.stream_name
  shard_count = 1 # Start with one shard for low volume
}

# 2. Create the S3 bucket to store processed data
resource "aws_s3_bucket" "processed_data_bucket" {
  bucket = var.s3_bucket_name
}

# 3. Define the IAM Role and Policies for the Lambda function
resource "aws_iam_role" "lambda_processing_role" {
  name = "kinesis-lambda-processor-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      }
    }]
  })
}

# Policy to allow Lambda to read from Kinesis and write logs
resource "aws_iam_role_policy" "lambda_kinesis_policy" {
  name = "lambda-kinesis-permissions"
  role = aws_iam_role.lambda_processing_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = [
          "kinesis:GetRecords",
          "kinesis:GetShardIterator",
          "kinesis:DescribeStream",
          "kinesis:ListStreams"
        ],
        Resource = aws_kinesis_stream.data_stream.arn
      },
      {
        Effect   = "Allow",
        Action   = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })
}

# Policy to allow Lambda to write to the S3 bucket
resource "aws_iam_role_policy" "lambda_s3_policy" {
  name = "lambda-s3-write-permissions"
  role = aws_iam_role.lambda_processing_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = ["s3:PutObject"],
        Resource = "${aws_s3_bucket.processed_data_bucket.arn}/*"
      }
    ]
  })
}

# 4. Create the Lambda function for processing
# Note: You need to provide the function's code in a 'processor.zip' file.
resource "aws_lambda_function" "kinesis_processor" {
  filename      = "processor.zip"
  function_name = "kinesis-data-processor"
  role          = aws_iam_role.lambda_processing_role.arn
  handler       = "index.handler" # Assuming a Node.js function in index.js
  runtime       = "nodejs18.x"
  source_code_hash = filebase64sha256("processor.zip")

  environment {
    variables = {
      S3_BUCKET_NAME = aws_s3_bucket.processed_data_bucket.bucket
    }
  }
}

# 5. Create the event source mapping to trigger Lambda from Kinesis
resource "aws_lambda_event_source_mapping" "kinesis_trigger" {
  event_source_arn  = aws_kinesis_stream.data_stream.arn
  function_name     = aws_lambda_function.kinesis_processor.arn
  starting_position = "LATEST"
}

# --- Example Lambda code (index.js) to be placed in processor.zip ---
/*
const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const s3Client = new S3Client({ region: process.env.AWS_REGION });

exports.handler = async (event) => {
  console.log('Received event:', JSON.stringify(event, null, 2));
  
  for (const record of event.Records) {
    // Kinesis data is base64 encoded
    const payload = Buffer.from(record.kinesis.data, 'base64').toString('ascii');
    console.log('Decoded payload:', payload);

    const params = {
      Bucket: process.env.S3_BUCKET_NAME,
      Key: `processed-data/${record.eventID}.json`,
      Body: payload,
      ContentType: 'application/json'
    };

    try {
      await s3Client.send(new PutObjectCommand(params));
      console.log(`Successfully processed and stored record ${record.eventID}`);
    } catch (err) {
      console.error(`Error processing record ${record.eventID}:`, err);
      // Handle error accordingly
    }
  }
  return `Successfully processed ${event.Records.length} records.`;
};
*/
Scenario 5: Private GKE Cluster with Cloud SQL 🔐
Title: Private GKE Cluster with Cloud SQL

Description: Deploy a secure, production-grade microservices application on a private Google Kubernetes Engine (GKE) cluster that communicates securely with a managed SQL database.

User Prompt: "Set up a secure Kubernetes environment that is not exposed to the internet and can connect to a Postgres database."

Services Involved:

GKE (Google Kubernetes Engine)

VPC Network

Cloud SQL (for PostgreSQL)

Cloud NAT (for outbound internet access)

IAM

Level: Advanced

Estimated Time: 60 minutes

Cost Level: High

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram is enclosed within a large box representing a VPC Network. Inside the VPC, there's a GKE Cluster labeled "Private Cluster". The nodes within the cluster have no public IP addresses. Pods running inside the GKE cluster need to talk to a database. An arrow points from the GKE cluster to a Cloud SQL for PostgreSQL instance. This connection is internal to the VPC, using private IP addresses. For the cluster to pull container images or hit external APIs, an arrow points from the GKE cluster to a Cloud NAT Gateway, which in turn points to the public Internet. This ensures nodes can make outbound requests without having a public IP address. A developer is shown outside the VPC, connecting to the cluster's control plane via a secured endpoint or a bastion host (not shown for simplicity).

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform code deploys a private GKE cluster and a Cloud SQL instance within the same VPC.

provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "project_id" {
  description = "The GCP project ID."
  type        = string
}

variable "region" {
  description = "The GCP region for all resources."
  default     = "us-central1"
}

# 1. Create a custom VPC Network
resource "google_compute_network" "private_vpc" {
  name                    = "gke-private-vpc"
  auto_create_subnetworks = false
}

# 2. Create a subnetwork for the GKE cluster
resource "google_compute_subnetwork" "gke_subnet" {
  name          = "gke-subnet"
  ip_cidr_range = "*********/20"
  region        = var.region
  network       = google_compute_network.private_vpc.id
  secondary_ip_range {
    range_name    = "pods-range"
    ip_cidr_range = "*********/16"
  }
  secondary_ip_range {
    range_name    = "services-range"
    ip_cidr_range = "*********/20"
  }
}

# 3. Create a Cloud Router and Cloud NAT for outbound internet access
resource "google_compute_router" "router" {
  name    = "gke-private-router"
  network = google_compute_network.private_vpc.id
  region  = var.region
}

resource "google_compute_router_nat" "nat" {
  name                               = "gke-private-nat"
  router                             = google_compute_router.router.name
  region                             = var.region
  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  nat_ip_allocate_option             = "AUTO_ONLY"
}

# 4. Create the private GKE cluster
resource "google_container_cluster" "private_cluster" {
  name     = "private-gke-cluster"
  location = var.region
  network  = google_compute_network.private_vpc.self_link
  subnetwork = google_compute_subnetwork.gke_subnet.self_link

  # Make the cluster private
  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = true # Control plane has no public IP
    master_ipv4_cidr_block  = "**********/28"
  }

  ip_allocation_policy {
    cluster_secondary_range_name  = "pods-range"
    services_secondary_range_name = "services-range"
  }
  
  # Allow control plane to access nodes for management
  master_authorized_networks_config {
    # No external networks are allowed by default
  }

  # Use a small, cost-effective node pool for the demo
  initial_node_count = 1
  remove_default_node_pool = true
}

resource "google_container_node_pool" "primary_nodes" {
  name       = "primary-node-pool"
  cluster    = google_container_cluster.private_cluster.name
  location   = var.region
  node_count = 1

  node_config {
    machine_type = "e2-medium"
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
  }
}

# 5. Create a private Cloud SQL for PostgreSQL instance
resource "google_sql_database_instance" "postgres_db" {
  name             = "private-postgres-db"
  database_version = "POSTGRES_14"
  region           = var.region

  settings {
    tier = "db-g1-small"
    ip_configuration {
      ipv4_enabled    = false # No public IP
      private_network = google_compute_network.private_vpc.id
    }
  }
  
  # Required to provision the private IP
  depends_on = [google_project_service.sqladmin]
}

# 6. Enable the necessary APIs
resource "google_project_service" "sqladmin" {
  project = var.project_id
  service = "sqladmin.googleapis.com"
}

--------
Here are two more detailed scenarios for your application.

Scenario 6: CI/CD Pipeline for Containers on Azure 🚀
Title: CI/CD Pipeline for Containers on Azure

Description: Automate the build and deployment of a containerized application from a Git repository to Azure App Service using Azure DevOps.

User Prompt: "I want to automatically update my web app whenever I push code to my repo."

Services Involved:

Azure App Service (for hosting the web app)

Azure Container Registry (ACR) (for storing Docker images)

Azure DevOps Pipelines (for CI/CD automation)

Azure Monitor (for observing application logs)

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: Medium

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram starts with a developer pushing code to a Git Repository (like Azure Repos or GitHub). This push triggers an Azure DevOps Pipeline. The pipeline is shown as a two-stage process. The first stage, "Build", takes the application code and a Dockerfile, builds a Docker image, and pushes it to an Azure Container Registry (ACR). An arrow points from the pipeline's build stage to ACR. The second stage, "Release", is triggered upon a successful build. This stage takes the newly pushed image from ACR and deploys it to an Azure App Service that is configured to run containers. An arrow points from the pipeline's release stage to the App Service. Finally, Azure Monitor is shown collecting logs and metrics from the App Service, providing observability.

Terraform Code (main.tf)
This Terraform code sets up the target infrastructure. The Azure DevOps Pipeline itself is configured within the Azure DevOps portal.

Terraform

# main.tf
# This Terraform code provisions the Azure infrastructure to host a containerized web app.
# The CI/CD pipeline is then configured in Azure DevOps to target these resources.

provider "azurerm" {
  features {}
}

variable "resource_group_name" {
  description = "The name of the resource group."
  default     = "rg-cicd-app-demo"
}

variable "location" {
  description = "The Azure region to deploy resources."
  default     = "East US"
}

variable "app_name_prefix" {
  description = "A unique prefix for naming resources."
}

# 1. Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = var.resource_group_name
  location = var.location
}

# 2. Create an Azure Container Registry (ACR) to store Docker images
resource "azurerm_container_registry" "acr" {
  name                = "${var.app_name_prefix}acr"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  sku                 = "Standard"
  admin_enabled       = true # Simplifies authentication for the pipeline
}

# 3. Create an App Service Plan (defines the underlying compute)
resource "azurerm_service_plan" "plan" {
  name                = "${var.app_name_prefix}-app-plan"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  os_type             = "Linux"
  sku_name            = "B1" # Basic tier for cost-effectiveness
}

# 4. Create the App Service for Linux (Web App for Containers)
resource "azurerm_linux_web_app" "app_service" {
  name                = "${var.app_name_prefix}-webapp"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  service_plan_id     = azurerm_service_plan.plan.id

  # This section configures the app to pull images from our ACR.
  # The pipeline will update the DOCKER_IMAGE_NAME_AND_TAG setting.
  site_config {
    always_on = false
    app_settings = {
      "DOCKER_REGISTRY_SERVER_URL"      = "https://${azurerm_container_registry.acr.login_server}"
      "DOCKER_REGISTRY_SERVER_USERNAME" = azurerm_container_registry.acr.admin_username
      "DOCKER_REGISTRY_SERVER_PASSWORD" = azurerm_container_registry.acr.admin_password
      "WEBSITES_ENABLE_APP_SERVICE_STORAGE" = "false"
    }
  }

  identity {
    type = "SystemAssigned"
  }
}

# Output the web app's URL
output "webapp_url" {
  value = "https://${azurerm_linux_web_app.app_service.default_hostname}"
}

# Output the ACR login server for the pipeline configuration
output "acr_login_server" {
  value = azurerm_container_registry.acr.login_server
}
Scenario 7: AWS Serverless Data Lake Querying 💧
Title: AWS Serverless Data Lake Querying

Description: Build a system to catalog raw data in S3 and run complex SQL queries on it without managing any servers or data warehouse infrastructure.

User Prompt: "I have TBs of log files in S3. I need an easy way to run SQL queries on them."

Services Involved:

S3 (for the data lake storage)

AWS Glue Crawler (to automatically discover schema and create a data catalog)

AWS Glue Data Catalog (as the metadata repository)

Amazon Athena (to run interactive SQL queries)

Level: Intermediate

Estimated Time: 25 minutes

Cost Level: Low (Pay-per-query)

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows an S3 Bucket labeled "Raw Data Lake" containing folders of data (e.g., CSV or Parquet files). An AWS Glue Crawler is shown with an arrow pointing to this S3 bucket, indicating it crawls the data. The crawler analyzes the data and populates an AWS Glue Data Catalog with table definitions and schema information; an arrow points from the crawler to the catalog. An Analyst/User is shown on the right using the Amazon Athena query editor. Athena is configured to use the Glue Data Catalog as its metastore. An arrow points from Athena to the Glue Data Catalog. When the user runs a query, Athena reads the schema from the catalog and then directly scans the relevant data in the S3 bucket to produce results. A final arrow points from Athena back to the S3 bucket.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform code creates a data lake foundation with S3, Glue, and Athena.

provider "aws" {
  region = "us-east-1"
}

variable "data_lake_bucket_name" {
  description = "A unique name for the S3 bucket that will be the data lake."
}

variable "athena_results_bucket_name" {
  description = "A unique name for the S3 bucket to store Athena query results."
}

# 1. Create the S3 bucket for the data lake
resource "aws_s3_bucket" "data_lake" {
  bucket = var.data_lake_bucket_name
}

# 2. Create the S3 bucket for Athena query results
resource "aws_s3_bucket" "athena_results" {
  bucket = var.athena_results_bucket_name
}

# 3. Create the IAM Role for the Glue Crawler
resource "aws_iam_role" "glue_crawler_role" {
  name = "glue-crawler-data-lake-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = {
        Service = "glue.amazonaws.com"
      }
    }]
  })
}

# Attach the AWS-managed policy that provides necessary permissions for Glue
resource "aws_iam_role_policy_attachment" "glue_service_policy" {
  role       = aws_iam_role.glue_crawler_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSGlueServiceRole"
}

# Attach a custom policy to allow access to the specific data lake bucket
resource "aws_iam_role_policy" "s3_access_policy" {
  name = "glue-s3-data-lake-access"
  role = aws_iam_role.glue_crawler_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = ["s3:GetObject", "s3:PutObject"],
        Resource = "${aws_s3_bucket.data_lake.arn}/*"
      }
    ]
  })
}

# 4. Create the Glue Database to hold our table metadata
resource "aws_glue_catalog_database" "data_lake_db" {
  name = "log_analytics_db"
}

# 5. Create the Glue Crawler to populate the catalog
resource "aws_glue_crawler" "data_lake_crawler" {
  name          = "data-lake-crawler"
  role          = aws_iam_role.glue_crawler_role.arn
  database_name = aws_glue_catalog_database.data_lake_db.name

  s3_target {
    path = "s3://${aws_s3_bucket.data_lake.bucket}"
  }

  # This configuration tells Glue how to handle schema changes over time
  schema_change_policy {
    update_behavior = "LOG"
    delete_behavior = "LOG"
  }
}

# 6. Set up the Athena workgroup
resource "aws_athena_workgroup" "primary" {
  name = "primary"

  configuration {
    result_configuration {
      output_location = "s3://${aws_s3_bucket.athena_results.bucket}/"
    }
  }
}
-
Of course. Here are two more scenarios.

Scenario 8: Intelligent Document Processing on GCP 📄
Title: Intelligent Document Processing on GCP

Description: Build a serverless pipeline that automatically extracts structured data from uploaded documents (like invoices or receipts) using Google Cloud's AI services.

User Prompt: "I need to automatically scan invoices and extract the total amount and due date."

Services Involved:

Cloud Storage (to receive and store documents)

Cloud Functions (to orchestrate the workflow)

Document AI (to process documents and extract entities)

BigQuery (to store the extracted, structured data)

Level: Advanced

Estimated Time: 50 minutes

Cost Level: Medium

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows a user uploading a PDF or image file into a Cloud Storage bucket labeled "Incoming Documents". This upload event triggers a Cloud Function. An arrow points from Cloud Storage to the Cloud Function. The function then calls the Document AI API, sending the document for analysis. An arrow points from the Cloud Function to Document AI. Document AI processes the file, extracts key entities (like names, dates, amounts), and returns a structured JSON object to the function. An arrow points back from Document AI to the Cloud Function. Finally, the Cloud Function takes this structured JSON data and inserts it as a new row into a BigQuery table labeled "Extracted Data". An arrow points from the function to BigQuery, where analysts can later run queries.

Terraform Code (main.tf)
This Terraform code sets up the required storage, database, and IAM roles. The Cloud Function code and Document AI processor must be created separately.

Terraform

# main.tf
# This Terraform code provisions the GCP infrastructure for a document processing pipeline.

provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "project_id" {
  description = "The GCP project ID."
}

variable "location" {
  description = "The GCP region for all resources."
  default     = "us-central1"
}

# 1. Enable necessary Google Cloud APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "storage.googleapis.com",
    "cloudfunctions.googleapis.com",
    "documentai.googleapis.com",
    "bigquery.googleapis.com",
    "cloudbuild.googleapis.com"
  ])
  project            = var.project_id
  service            = each.key
  disable_on_destroy = false
}

# 2. Create Cloud Storage buckets
resource "google_storage_bucket" "incoming_documents" {
  name          = "${var.project_id}-doc-uploads"
  location      = "US" # Document AI often requires multi-region buckets
  force_destroy = true
}

resource "google_storage_bucket" "functions_source" {
  name     = "${var.project_id}-functions-source"
  location = var.location
}

# 3. Create a BigQuery dataset and table to store extracted data
resource "google_bigquery_dataset" "doc_data" {
  dataset_id = "extracted_document_data"
  location   = var.location
}

resource "google_bigquery_table" "invoices" {
  dataset_id = google_bigquery_dataset.doc_data.dataset_id
  table_id   = "invoices"
  schema = jsonencode([
    {
      "name": "file_name",
      "type": "STRING",
      "mode": "NULLABLE"
    },
    {
      "name": "invoice_id",
      "type": "STRING",
      "mode": "NULLABLE"
    },
    {
      "name": "total_amount",
      "type": "FLOAT64",
      "mode": "NULLABLE"
    },
    {
      "name": "due_date",
      "type": "DATE",
      "mode": "NULLABLE"
    }
  ])
}

# 4. Create the Cloud Function triggered by Cloud Storage
# Note: This assumes you have the function source code in a 'source.zip' file.
data "archive_file" "source" {
  type        = "zip"
  source_dir  = "function_source/" # A local directory with your function
  output_path = "/tmp/source.zip"
}

resource "google_storage_bucket_object" "zip" {
  name   = "source.zip"
  bucket = google_storage_bucket.functions_source.name
  source = data.archive_file.source.output_path
}

resource "google_cloudfunctions_function" "doc_processor_function" {
  name        = "document-processor"
  runtime     = "python310"
  entry_point = "process_document"
  
  event_trigger {
    event_type = "google.storage.object.finalize"
    resource   = google_storage_bucket.incoming_documents.name
  }

  source_archive_bucket = google_storage_bucket.functions_source.name
  source_archive_object = google_storage_bucket_object.zip.name

  # Add environment variables for the function
  environment_variables = {
    PROJECT_ID         = var.project_id
    LOCATION           = "us" # Document AI processor location
    PROCESSOR_ID       = "your-doc-ai-processor-id" # MUST be created in the GCP console first
    BIGQUERY_DATASET   = google_bigquery_dataset.doc_data.dataset_id
    BIGQUERY_TABLE     = google_bigquery_table.invoices.table_id
  }

  depends_on = [google_project_service.apis]
}
Scenario 9: Multi-Region Active-Active Web Application on AWS 🌍
Title: Multi-Region Active-Active Web Application

Description: Design a highly available and fault-tolerant architecture that serves traffic from multiple AWS regions simultaneously, providing low latency for global users and resilience against regional outages.

User Prompt: "Build me a bulletproof global application that never goes down."

Services Involved:

Route 53 (with latency-based routing)

CloudFront (for caching and WAF integration)

Application Load Balancer (ALB)

EC2 Auto Scaling Groups

DynamoDB Global Tables (for multi-region, active-active database)

Level: Advanced

Estimated Time: 90 minutes

Cost Level: High

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows a global user connecting to Route 53, which uses a latency-based routing policy. Route 53 directs the user to the closest of two AWS Regions (e.g., us-east-1 and eu-west-1). Each region's architecture is a mirror of the other. Within each region, traffic flows through an Application Load Balancer, which distributes requests across an EC2 Auto Scaling Group. The EC2 instances run the application code. These instances read from and write to a local replica of a DynamoDB Global Table. A bi-directional arrow between the DynamoDB tables in each region indicates that data written in one region is automatically replicated to the other, keeping them in sync. This setup ensures that if one entire region fails, Route 53 health checks will automatically failover all traffic to the healthy region.

Terraform Code (main.tf)
This is a simplified example showing the core components in two regions. A production setup would be more complex.

Terraform

# main.tf
# This Terraform code provisions a multi-region active-active architecture.

# --- Provider for Region 1: us-east-1 ---
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# --- Provider for Region 2: eu-west-1 ---
provider "aws" {
  alias  = "eu_west_1"
  region = "eu-west-1"
}

variable "domain_name" {
  description = "The root domain name for the application."
}

# 1. Create the DynamoDB Global Table
resource "aws_dynamodb_global_table" "global_app_table" {
  name = "app-global-table"
  
  # Define replicas in each region
  replica {
    region_name = "us-east-1"
  }

  replica {
    region_name = "eu-west-1"
  }
}

# --- Infrastructure in us-east-1 ---
module "region_us_east_1" {
  source    = "./region" # Assumes a reusable module in a 'region' sub-directory
  providers = {
    aws = aws.us_east_1
  }

  region       = "us-east-1"
  app_name     = "global-app-use1"
  table_arn    = aws_dynamodb_global_table.global_app_table.arn
}

# --- Infrastructure in eu-west-1 ---
module "region_eu_west_1" {
  source    = "./region"
  providers = {
    aws = aws.eu_west_1
  }
  
  region       = "eu-west-1"
  app_name     = "global-app-euw1"
  table_arn    = aws_dynamodb_global_table.global_app_table.arn
}

# 3. Configure Route 53 for Latency-Based Routing
data "aws_route53_zone" "primary" {
  name = var.domain_name
}

resource "aws_route53_record" "latency_record_us_east_1" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "app.${var.domain_name}"
  type    = "A"

  latency_routing_policy {
    region = "us-east-1"
  }
  
  set_identifier = "us-east-1-region"

  alias {
    name                   = module.region_us_east_1.lb_dns_name
    zone_id                = module.region_us_east_1.lb_zone_id
    evaluate_target_health = true
  }
}

resource "aws_route53_record" "latency_record_eu_west_1" {
  zone_id = data.aws_route53_zone.primary.zone_id
  name    = "app.${var.domain_name}"
  type    = "A"

  latency_routing_policy {
    region = "eu-west-1"
  }

  set_identifier = "eu-west-1-region"

  alias {
    name                   = module.region_eu_west_1.lb_dns_name
    zone_id                = module.region_eu_west_1.lb_zone_id
    evaluate_target_health = true
  }
}

# --- Example of a reusable regional module (./region/main.tf) ---
/*
variable "region" {}
variable "app_name" {}
variable "table_arn" {}

# Get VPC, subnets, AMI etc.
# ...

resource "aws_lb" "app_lb" { ... }
resource "aws_autoscaling_group" "app_asg" { ... }

output "lb_dns_name" { value = aws_lb.app_lb.dns_name }
output "lb_zone_id" { value = aws_lb.app_lb.zone_id }
*/

_-----------
Of course. Here are two more scenarios.

Scenario 10: Azure IoT Data Ingestion and Monitoring 🌡️
Title: Azure IoT Data Ingestion and Monitoring

Description: Build a scalable solution to ingest telemetry data from millions of IoT devices, process it in real-time, and store it for analysis and visualization.

User Prompt: "I need to collect temperature data from sensors and show it on a dashboard."

Services Involved:

IoT Hub (for device registration and data ingestion)

Stream Analytics (for real-time data processing and filtering)

Azure Cosmos DB (to store the processed data)

Power BI (for data visualization and dashboards)

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Medium

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows multiple IoT Devices on the left, sending telemetry data securely to an Azure IoT Hub. The IoT Hub acts as a cloud gateway, managing device identities and ingesting the data stream. An arrow points from the IoT Hub to an Azure Stream Analytics Job. This job runs a continuous SQL-like query to process the data in real-time (e.g., filter for alerts or aggregate data). The Stream Analytics job has two outputs. The primary output is an arrow pointing to Azure Cosmos DB, where the processed telemetry is stored for long-term retention and querying. The second output is an arrow pointing to Power BI, which provides a live dashboard visualization of the incoming data stream.

Terraform Code (main.tf)
This Terraform code provisions the core Azure infrastructure. The Stream Analytics query and Power BI connection are configured in the Azure portal.

Terraform

# main.tf
# This Terraform code provisions the infrastructure for an Azure IoT solution.

provider "azurerm" {
  features {}
}

variable "resource_group_name" {
  description = "The name of the resource group."
  default     = "rg-iot-pipeline-demo"
}

variable "location" {
  description = "The Azure region to deploy resources."
  default     = "East US"
}

variable "app_name_prefix" {
  description = "A unique prefix for naming resources."
}

# 1. Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = var.resource_group_name
  location = var.location
}

# 2. Create an IoT Hub to ingest data from devices
resource "azurerm_iothub" "hub" {
  name                = "${var.app_name_prefix}-iothub"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  
  sku {
    name     = "S1"
    capacity = 1
  }
}

# 3. Create a Cosmos DB account and container to store processed data
resource "azurerm_cosmosdb_account" "db" {
  name                = "${var.app_name_prefix}-cosmosdb"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  offer_type          = "Standard"
  kind                = "GlobalDocumentDB"

  consistency_policy {
    consistency_level = "Session"
  }

  geo_location {
    location          = azurerm_resource_group.rg.location
    failover_priority = 0
  }
}

resource "azurerm_cosmosdb_sql_database" "db_database" {
  name                = "DeviceDataDB"
  resource_group_name = azurerm_cosmosdb_account.db.resource_group_name
  account_name        = azurerm_cosmosdb_account.db.name
}

resource "azurerm_cosmosdb_sql_container" "db_container" {
  name                = "Telemetry"
  resource_group_name = azurerm_cosmosdb_account.db.resource_group_name
  account_name        = azurerm_cosmosdb_account.db.name
  database_name       = azurerm_cosmosdb_sql_database.db_database.name
  partition_key_path  = "/deviceId"
  throughput          = 400
}

# 4. Create a Stream Analytics Job
resource "azurerm_stream_analytics_job" "processor" {
  name                                     = "${var.app_name_prefix}-streamjob"
  resource_group_name                      = azurerm_resource_group.rg.name
  location                                 = azurerm_resource_group.rg.location
  streaming_units                          = 1
  
  # The Transformation Query is defined in the portal.
  # Example: SELECT * INTO [cosmos-output] FROM [iothub-input] WHERE Temperature > 25
  transformation_query = "SELECT\n    *\nINTO\n    [output]\nFROM\n    [input]"
}

# 5. Define the input for the Stream Analytics Job (from IoT Hub)
resource "azurerm_stream_analytics_stream_input_iothub" "input_iothub" {
  name                                 = "iothub-input"
  stream_analytics_job_name            = azurerm_stream_analytics_job.processor.name
  resource_group_name                  = azurerm_resource_group.rg.name
  endpoint                             = "messages/events"
  eventhub_consumer_group_name         = "$Default"
  iothub_namespace                     = azurerm_iothub.hub.name
  shared_access_key_name               = "iothubowner"
  shared_access_key                    = azurerm_iothub.hub.shared_access_policy[0].primary_key
  
  serialization {
    type     = "Json"
    encoding = "UTF8"
  }
}

# 6. Define the output for the Stream Analytics Job (to Cosmos DB)
resource "azurerm_stream_analytics_output_cosmosdb" "output_cosmos" {
  name                      = "cosmos-output"
  stream_analytics_job_name = azurerm_stream_analytics_job.processor.name
  resource_group_name       = azurerm_resource_group.rg.name
  account_name              = azurerm_cosmosdb_account.db.name
  account_key               = azurerm_cosmosdb_account.db.primary_key
  database                  = azurerm_cosmosdb_sql_database.db_database.name
  container_name            = azurerm_cosmosdb_sql_container.db_container.name
  document_id               = "id"
  partition_key             = "deviceId"
}
Scenario 11: Big Data ETL Pipeline with GCP Dataproc 🐘
Title: Big Data ETL Pipeline with GCP Dataproc

Description: Design a batch processing pipeline to run large-scale data transformations using a managed Apache Spark cluster on Google Cloud.

User Prompt: "I need to process terabytes of raw data nightly using a Spark job."

Services Involved:

Cloud Storage (for staging raw and processed data)

Dataproc (for managed Hadoop/Spark clusters)

Cloud Composer / Cloud Functions (to orchestrate and schedule the job)

BigQuery (as the final data warehouse)

Level: Advanced

Estimated Time: 75 minutes

Cost Level: High (while cluster is running)

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram shows a workflow that begins with raw data (e.g., log files) being loaded into a Cloud Storage bucket labeled "Raw Data". A Cloud Composer (managed Airflow) DAG or a scheduled Cloud Function triggers the pipeline. An arrow points from the scheduler to a Dataproc cluster. The scheduler submits a Spark job to the Dataproc cluster. The Dataproc cluster spins up, reads the raw data from the first Cloud Storage bucket, performs large-scale transformations, and writes the processed, clean data back to a different Cloud Storage bucket labeled "Processed Data". Arrows show this data flow from GCS to Dataproc and back to GCS. Finally, a separate step in the workflow loads the processed data from the second GCS bucket into a BigQuery table for analysis.

Terraform Code (main.tf)
This code provisions the Dataproc cluster and related storage. It assumes a Spark job (e.g., a .py or .jar file) is already available in a GCS bucket.

Terraform

# main.tf
# This Terraform code provisions a Dataproc cluster for running batch Spark jobs.

provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "project_id" {
  description = "The GCP project ID."
}

variable "region" {
  description = "The GCP region for all resources."
  default     = "us-central1"
}

variable "cluster_name" {
  description = "Name for the Dataproc cluster."
  default     = "spark-etl-cluster"
}

# 1. Enable necessary APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "dataproc.googleapis.com",
    "storage.googleapis.com",
  ])
  project            = var.project_id
  service            = each.key
  disable_on_destroy = false
}

# 2. Create a Cloud Storage bucket for Spark jobs and data
resource "google_storage_bucket" "dataproc_bucket" {
  name          = "${var.project_id}-dataproc-staging"
  location      = var.region
  force_destroy = true
}

# 3. Create the Dataproc cluster
resource "google_dataproc_cluster" "spark_cluster" {
  name   = var.cluster_name
  region = var.region
  
  cluster_config {
    staging_bucket = google_storage_bucket.dataproc_bucket.name

    master_config {
      num_instances = 1
      machine_type  = "e2-standard-2"
    }

    worker_config {
      num_instances = 2
      machine_type  = "e2-standard-2"
    }
    
    # Configure the cluster to be ephemeral by setting a max idle time
    # This will automatically delete the cluster to save costs.
    lifecycle_config {
      idle_delete_ttl = "1800s" # Delete after 30 minutes of idle time
    }
  }
}

# 4. Define a Dataproc job to be submitted to the cluster
# This resource submits the job. You would typically trigger this from a scheduler.
resource "google_dataproc_job" "spark_job" {
  project = var.project_id
  region  = var.region
  
  placement {
    cluster_name = google_dataproc_cluster.spark_cluster.name
  }

  spark_job {
    main_class = "org.apache.spark.examples.SparkPi" # Example job
    # For a real job, you would use a JAR file you've uploaded
    # jar_file_uris = ["gs://your-bucket/your-spark-job.jar"] 
    args = ["1000"]
  }
}

# Output the cluster name
output "dataproc_cluster_name" {
  value = google_dataproc_cluster.spark_cluster.name
}

# Output the Dataproc job ID
output "dataproc_job_id" {
  value = google_dataproc_job.spark_job.id
}


------------
Scenario 12: Secure Bastion Host Access on AWS 🛡️
Title: Secure Bastion Host Access on AWS

Description: Implement a secure method to access private resources (like databases or backend servers) in a VPC using a hardened bastion host and AWS Systems Manager for shell access, eliminating the need for open SSH ports.

User Prompt: "How can I securely SSH into my private EC2 instances without exposing them to the internet?"

Services Involved:

VPC (with public and private subnets)

EC2 (for the bastion host and private instances)

Security Groups

AWS Systems Manager (SSM) Session Manager

IAM

Level: Intermediate

Estimated Time: 40 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows an AWS VPC divided into a Public Subnet and a Private Subnet. An Internet Gateway is attached to the VPC, with a route from the public subnet.

An Administrator is shown outside the VPC. They connect to AWS via the AWS Systems Manager (SSM) Session Manager service in the cloud. SSM then establishes a secure, tunneled connection to an EC2 instance labeled "Bastion Host" located in the public subnet. This bastion host does not have an open SSH port (port 22) in its security group.

Inside the private subnet, there's another EC2 instance labeled "Private Server". The bastion host's security group allows outbound SSH traffic (port 22) specifically to the private server's security group. An arrow shows this SSH connection originating from the bastion and terminating at the private server, all within the VPC. This setup allows the administrator to first connect to the bastion via SSM and then "jump" to the private server.

Terraform Code (main.tf)
This Terraform code provisions the network, bastion host, and a private instance, complete with the necessary IAM and security group configurations for SSM access.

Terraform

# main.tf
# This Terraform provisions a VPC with a bastion host accessed via AWS Systems Manager.

provider "aws" {
  region = "us-east-1"
}

# 1. Create a VPC
resource "aws_vpc" "main" {
  cidr_block = "10.0.0.0/16"
  tags = { Name = "main-vpc" }
}

# 2. Create public and private subnets
resource "aws_subnet" "public" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = "********/24"
  map_public_ip_on_launch = true
  tags = { Name = "public-subnet" }
}

resource "aws_subnet" "private" {
  vpc_id     = aws_vpc.main.id
  cidr_block = "********/24"
  tags = { Name = "private-subnet" }
}

# 3. Create Internet Gateway and routing for the public subnet
resource "aws_internet_gateway" "gw" {
  vpc_id = aws_vpc.main.id
}

resource "aws_route_table" "public_rt" {
  vpc_id = aws_vpc.main.id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.gw.id
  }
}

resource "aws_route_table_association" "public" {
  subnet_id      = aws_subnet.public.id
  route_table_id = aws_route_table.public_rt.id
}

# 4. IAM role that allows EC2 to be managed by Systems Manager
resource "aws_iam_role" "ssm_role" {
  name = "ec2-ssm-role"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "ec2.amazonaws.com" }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "ssm_policy" {
  role       = aws_iam_role.ssm_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_instance_profile" "ssm_profile" {
  name = "ssm-instance-profile"
  role = aws_iam_role.ssm_role.name
}

# 5. Security Groups
resource "aws_security_group" "bastion_sg" {
  name   = "bastion-sg"
  vpc_id = aws_vpc.main.id
  # No inbound SSH rule. Access is via SSM.
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_security_group" "private_sg" {
  name   = "private-sg"
  vpc_id = aws_vpc.main.id
  # Allow inbound SSH only from the bastion's security group
  ingress {
    from_port       = 22
    to_port         = 22
    protocol        = "tcp"
    security_groups = [aws_security_group.bastion_sg.id]
  }
}

# 6. Create the EC2 instances
data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]
  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }
}

resource "aws_instance" "bastion" {
  ami           = data.aws_ami.amazon_linux.id
  instance_type = "t2.micro"
  subnet_id     = aws_subnet.public.id
  vpc_security_group_ids = [aws_security_group.bastion_sg.id]
  iam_instance_profile = aws_iam_instance_profile.ssm_profile.name
  tags = { Name = "BastionHost" }
}

resource "aws_instance" "private_server" {
  ami           = data.aws_ami.amazon_linux.id
  instance_type = "t2.micro"
  subnet_id     = aws_subnet.private.id
  vpc_security_group_ids = [aws_security_group.private_sg.id]
  tags = { Name = "PrivateServer" }
}
Scenario 13: GCP Media Streaming and Transcoding Pipeline 🎬
Title: GCP Media Streaming and Transcoding Pipeline

Description: Create an automated pipeline that transcodes uploaded video files into multiple formats and bitrates suitable for adaptive streaming on web and mobile devices.

User Prompt: "When I upload a high-res video, I need it converted into different qualities for my streaming app."

Services Involved:

Cloud Storage (for storing raw and transcoded media)

Cloud Functions (to trigger the transcoding job)

Transcoder API (for high-quality video transcoding)

Cloud CDN (to serve the final media content)

Level: Advanced

Estimated Time: 50 minutes

Cost Level: Medium

Export Formats: PNG, Terraform

Architecture Diagram (PNG) Description
The diagram starts with a user uploading a large video file to a Cloud Storage bucket labeled "Raw Media". This upload event triggers a Cloud Function. The Cloud Function's role is to start a transcoding job. An arrow points from the function to the Transcoder API. The function provides the Transcoder API with the location of the raw video file and a "transcoding template" that defines the desired output formats (e.g., 1080p, 720p, 480p HLS streams).

The Transcoder API then reads the raw video from the first bucket, performs the transcoding, and writes the resulting video segments and manifest files into a second Cloud Storage bucket labeled "Processed Media". An arrow shows this data flow. This second bucket is configured as the backend for a Cloud CDN. Finally, an end-user's device is shown streaming the video content directly from the Cloud CDN, which provides low-latency delivery by caching content at Google's edge locations.

Terraform Code (main.tf)
This Terraform code sets up the storage buckets and the Cloud Function trigger. The Transcoder API job templates are typically configured in the GCP console.

Terraform

# main.tf
# This Terraform provisions the infrastructure for a media transcoding pipeline on GCP.

provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "project_id" {
  description = "The GCP project ID."
}

variable "location" {
  description = "The GCP region for all resources."
  default     = "us-central1"
}

# 1. Enable necessary APIs
resource "google_project_service" "apis" {
  for_each = toset([
    "storage.googleapis.com",
    "cloudfunctions.googleapis.com",
    "transcoder.googleapis.com",
    "cloudbuild.googleapis.com"
  ])
  project            = var.project_id
  service            = each.key
  disable_on_destroy = false
}

# 2. Create Cloud Storage buckets for raw and processed media
resource "google_storage_bucket" "raw_media" {
  name          = "${var.project_id}-raw-media"
  location      = var.location
  force_destroy = true
}

resource "google_storage_bucket" "processed_media" {
  name          = "${var.project_id}-processed-media-cdn"
  location      = var.location
  force_destroy = true
}

# Make the processed media bucket public for CDN access
resource "google_storage_bucket_iam_member" "public_access" {
  bucket = google_storage_bucket.processed_media.name
  role   = "roles/storage.objectViewer"
  member = "allUsers"
}

# 3. Create the Cloud Function to trigger transcoding
# Note: Assumes function source code is in a 'source.zip' file.
data "archive_file" "source" {
  type        = "zip"
  source_dir  = "function_source/" # Local directory with function code
  output_path = "/tmp/transcoder_source.zip"
}

resource "google_storage_bucket" "functions_source" {
  name     = "${var.project_id}-transcoder-functions-source"
  location = var.location
}

resource "google_storage_bucket_object" "zip" {
  name   = "source.zip"
  bucket = google_storage_bucket.functions_source.name
  source = data.archive_file.source.output_path
}

resource "google_cloudfunctions_function" "transcoder_trigger" {
  name        = "transcoder-trigger-function"
  runtime     = "python310"
  entry_point = "start_transcode_job"
  
  event_trigger {
    event_type = "google.storage.object.finalize"
    resource   = google_storage_bucket.raw_media.name
  }

  source_archive_bucket = google_storage_bucket.functions_source.name
  source_archive_object = google_storage_bucket_object.zip.name

  environment_variables = {
    PROJECT_ID         = var.project_id
    LOCATION           = var.location
    OUTPUT_BUCKET_NAME = google_storage_bucket.processed_media.name
    TRANSCODER_TEMPLATE_ID = "your-preset-template-id" # e.g., 'preset/web-hd' or a custom one
  }
}

# 4. Set up the CDN to serve the processed media
resource "google_compute_backend_bucket" "media_backend" {
  name        = "media-cdn-backend"
  bucket_name = google_storage_bucket.processed_media.name
  enable_cdn  = true
}

resource "google_compute_url_map" "default" {
  name            = "media-url-map"
  default_service = google_compute_backend_bucket.media_backend.self_link
}

resource "google_compute_target_http_proxy" "default" {
  name    = "media-http-proxy"
  url_map = google_compute_url_map.default.self_link
}

resource "google_compute_global_forwarding_rule" "default" {
  name       = "media-forwarding-rule"
  target     = google_compute_target_http_proxy.default.self_link
  port_range = "80"
}

-------------


Scenario 14: AWS API Gateway with VPC Link 🔗
Title: AWS API Gateway with VPC Link

Description: Expose a private service running on EC2 instances within a VPC to the public through a secure, managed API Gateway without exposing the instances to the internet.

User Prompt: "I need to create a public REST API for a service running on private servers."

Services Involved:

API Gateway (HTTP API type)

VPC Link

Network Load Balancer (NLB)

EC2 (running in a private subnet)

VPC

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a user making a request to an AWS API Gateway public endpoint. The API Gateway has an integration configured to use a VPC Link. The VPC Link points to a Network Load Balancer (NLB) that resides in a public subnet inside a VPC. The NLB's target group consists of EC2 instances running the actual application, but these instances are located in a private subnet within the same VPC. Arrows show the flow: User -> API Gateway -> VPC Link -> NLB -> Private EC2 Instances. This architecture ensures that only the NLB is exposed within the VPC for the API Gateway to connect to, while the application servers remain completely private.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform provisions an API Gateway that connects to a private service via a VPC Link.

provider "aws" {
  region = "us-east-1"
}

# Assume existing VPC and Subnets
data "aws_vpc" "default" {
  default = true
}

data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
  # Add filter for private subnets if tagged appropriately
}

data "aws_subnets" "public" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
  # Add filter for public subnets if tagged appropriately
}

# 1. Create a Network Load Balancer (NLB) in the public subnet
resource "aws_lb" "private_app_nlb" {
  name               = "private-app-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = data.aws_subnets.public.ids
}

resource "aws_lb_target_group" "private_app_tg" {
  name     = "private-app-tg"
  port     = 80
  protocol = "TCP"
  vpc_id   = data.aws_vpc.default.id
}

# 2. Create the VPC Link for API Gateway (HTTP APIs)
resource "aws_apigatewayv2_vpc_link" "api_link" {
  name               = "api-to-nlb-link"
  security_group_ids = [] # Can be specified for more control
  subnet_ids         = data.aws_subnets.private.ids
}

# 3. Create the HTTP API Gateway
resource "aws_apigatewayv2_api" "http_api" {
  name          = "private-service-api"
  protocol_type = "HTTP"
}

# 4. Create the integration between the API and the VPC Link
resource "aws_apigatewayv2_integration" "api_integration" {
  api_id = aws_apigatewayv2_api.http_api.id

  integration_type        = "HTTP_PROXY"
  integration_method      = "ANY"
  connection_type         = "VPC_LINK"
  connection_id           = aws_apigatewayv2_vpc_link.api_link.id
  integration_uri         = aws_lb.private_app_nlb.arn
}

# 5. Create a default route for all requests
resource "aws_apigatewayv2_route" "default_route" {
  api_id = aws_apigatewayv2_api.http_api.id
  route_key = "ANY /{proxy+}"
  target    = "integrations/${aws_apigatewayv2_integration.api_integration.id}"
}

# 6. Create a deployment stage
resource "aws_apigatewayv2_stage" "default_stage" {
  api_id = aws_apigatewayv2_api.http_api.id
  name   = "$default"
  auto_deploy = true
}

# Output the API endpoint URL
output "api_endpoint" {
  value = aws_apigatewayv2_stage.default_stage.invoke_url
}
Scenario 15: Azure Blue/Green Deployment for App Service 🔵🟢
Title: Azure Blue/Green Deployment for App Service

Description: Implement a zero-downtime deployment strategy by using deployment slots. Deploy a new version of an application to a staging slot, test it, and then swap it into production seamlessly.

User Prompt: "How can I update my production web app without any downtime for my users?"

Services Involved:

Azure App Service (Standard tier or higher)

App Service Plan

Deployment Slots

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an Azure App Service with two slots. The main box is the Production Slot, which has the public-facing URL (app.azurewebsites.net) and is labeled "Blue". It receives 100% of the live user traffic. A second, smaller box is attached to it, labeled Staging Slot ("Green"), with its own separate URL (app-staging.azurewebsites.net).

A CI/CD pipeline is shown deploying the new version of the code only to the Staging Slot. After deployment, automated tests can be run against the staging URL. Once validated, an operator clicks a "Swap" button in the Azure portal. An arrow indicates the swap action, which instantly redirects the production URL traffic from the Blue slot to the Green slot. The old Blue slot now contains the previous version of the code and receives no traffic, acting as an instant rollback target.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform provisions an Azure App Service with a staging slot for blue/green deployments.

provider "azurerm" {
  features {}
}

variable "prefix" {
  default = "bluegreen"
}

variable "location" {
  default = "East US"
}

# Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = "${var.prefix}-rg"
  location = var.location
}

# Create an App Service Plan (must be Standard or higher for slots)
resource "azurerm_service_plan" "plan" {
  name                = "${var.prefix}-plan"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  os_type             = "Linux"
  sku_name            = "S1" # Standard tier
}

# Create the main production web app (the 'blue' slot)
resource "azurerm_linux_web_app" "production_slot" {
  name                = "${var.prefix}-webapp"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  service_plan_id     = azurerm_service_plan.plan.id

  site_config {} # Configuration can be added here
}

# Create the staging slot (the 'green' slot)
resource "azurerm_linux_web_app_slot" "staging_slot" {
  name           = "staging"
  app_service_id = azurerm_linux_web_app.production_slot.id

  site_config {}
}

# Output the URLs for both slots
output "production_url" {
  value = "https://${azurerm_linux_web_app.production_slot.default_hostname}"
}

output "staging_url" {
  value = "https://${azurerm_linux_web_app_slot.staging_slot.default_hostname}"
}
Scenario 16: GCP Real-time Analytics with Looker 📊
Title: GCP Real-time Analytics with Looker

Description: Connect Google's BI platform, Looker, directly to a BigQuery data warehouse to build interactive, real-time dashboards and perform deep-dive data exploration.

User Prompt: "I need a powerful BI tool to create dashboards from my data in BigQuery."

Services Involved:

BigQuery (as the data warehouse)

Looker (for BI and visualization)

IAM (for secure connection)

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: High (Looker licensing)

Architecture Diagram (PNG) Description
The diagram shows data from various sources (e.g., Cloud Storage, Streaming Pipelines) being loaded into Google BigQuery, which is depicted as a large data warehouse. Separately, a Looker Instance is shown. A dedicated IAM Service Account is created in GCP with permissions to read data from BigQuery. An arrow points from the service account to BigQuery. The Looker instance is then configured with the credentials of this service account, creating a secure connection. An arrow points from Looker to BigQuery, labeled "Live Query". An analyst is shown using the Looker web interface to build dashboards. When a user interacts with a Looker dashboard, Looker generates SQL queries in real-time and sends them directly to BigQuery, which returns the results to be visualized.

Terraform Code (main.tf)
This Terraform code sets up the BigQuery dataset and the dedicated service account for Looker to use. The connection is then finalized within the Looker UI.

Terraform

# main.tf
# This Terraform prepares the GCP environment for a Looker connection.

provider "google" {
  project = "your-gcp-project-id"
  region  = "US"
}

variable "project_id" {
  description = "The GCP Project ID"
}

variable "dataset_id" {
  description = "The ID for the BigQuery dataset."
  default     = "sales_data"
}

# 1. Create the BigQuery dataset that Looker will connect to
resource "google_bigquery_dataset" "analytics_dataset" {
  dataset_id = var.dataset_id
  location   = "US"
  description = "Dataset for Looker BI analytics"
}

# 2. Create a dedicated service account for Looker
resource "google_service_account" "looker_sa" {
  account_id   = "looker-connector-sa"
  display_name = "Service Account for Looker"
}

# 3. Grant the service account the 'BigQuery Data Viewer' role on the dataset
resource "google_bigquery_dataset_iam_member" "looker_viewer" {
  dataset_id = google_bigquery_dataset.analytics_dataset.dataset_id
  role       = "roles/bigquery.dataViewer"
  member     = "serviceAccount:${google_service_account.looker_sa.email}"
}

# 4. Grant the service account the 'BigQuery Job User' role on the project
# This is required for Looker to be able to run queries.
resource "google_project_iam_member" "looker_job_user" {
  project = var.project_id
  role    = "roles/bigquery.jobUser"
  member  = "serviceAccount:${google_service_account.looker_sa.email}"
}

# 5. Create a key for the service account
# This key will be uploaded to the Looker UI to establish the connection.
resource "google_service_account_key" "looker_sa_key" {
  service_account_id = google_service_account.looker_sa.name
}

# Output the service account email and the private key (handle with care)
output "looker_service_account_email" {
  value = google_service_account.looker_sa.email
}

output "looker_service_account_key" {
  value     = base64decode(google_service_account_key.looker_sa_key.private_key)
  sensitive = true
}
Scenario 17: AWS Cost Anomaly Detection 💰
Title: AWS Cost Anomaly Detection

Description: Proactively monitor your AWS spending and get alerted automatically when unusual or unexpected cost increases are detected.

User Prompt: "Notify me immediately if my AWS bill suddenly spikes."

Services Involved:

AWS Cost Explorer

AWS Cost Anomaly Detection

Amazon SNS (Simple Notification Service)

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows the AWS Cost Explorer service at the center, which collects billing and usage data from all AWS services being used. An AWS Cost Anomaly Detection monitor is configured within Cost Explorer. This monitor continuously analyzes spending patterns using machine learning. When the monitor detects an anomaly (a deviation from the expected pattern), it triggers an action. An arrow points from the anomaly detection monitor to an Amazon SNS Topic. The SNS topic then sends a notification to all its subscribers. An email icon and a phone icon are shown subscribed to the topic, representing email and SMS alerts being sent to the finance or operations team.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform sets up AWS Cost Anomaly Detection and SNS alerting.

provider "aws" {
  region = "us-east-1"
}

# 1. Create an SNS Topic to send alerts to
resource "aws_sns_topic" "cost_alerts" {
  name = "cost-anomaly-alerts"
}

# 2. Create an email subscription for the SNS topic
# AWS will send a confirmation email to this address.
resource "aws_sns_topic_subscription" "email_target" {
  topic_arn = aws_sns_topic.cost_alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>" # Replace with your email
}

# 3. Create the Cost Anomaly Monitor
# This monitor will look for anomalies across all AWS services.
resource "aws_ce_anomaly_monitor" "service_monitor" {
  name          = "all-aws-services-monitor"
  monitor_type  = "CUSTOM"
  
  monitor_specification = jsonencode({
    "Dimensions" = {
      "Key" = "SERVICE",
      "Values" = [] # Empty means monitor all services
    }
  })
}

# 4. Create the Cost Anomaly Subscription to link the monitor to the SNS topic
resource "aws_ce_anomaly_subscription" "anomaly_alert_subscription" {
  name             = "daily-summary-alerts"
  frequency        = "DAILY"
  monitor_arn_list = [aws_ce_anomaly_monitor.service_monitor.arn]

  # Set the alert threshold
  threshold = 100 # Alert if an anomaly of $100 or more is detected

  subscriber {
    type    = "SNS"
    address = aws_sns_topic.cost_alerts.arn
  }
}
Scenario 18: Azure Logic Apps for Business Process Automation ⚙️
Title: Azure Logic Apps for Business Process Automation

Description: Automate a business workflow using Azure's low-code/no-code integration platform. This example shows how to save email attachments from Office 365 directly to Azure Blob Storage.

User Prompt: "I need a simple way to automatically save all attachments from my invoices email to cloud storage."

Services Involved:

Azure Logic Apps

Office 365 Outlook Connector

Azure Blob Storage Connector

Level: Beginner

Estimated Time: 20 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram is a visual workflow designer. The first block is the Trigger, labeled "When a new email arrives (Office 365)" with properties like "Folder: Inbox" and "Has Attachment: Yes". An arrow points down to a Loop block labeled "For each attachment". Inside the loop, there is an Action block labeled "Create blob (Azure Storage)". This block is configured to use the "Attachment Name" and "Attachment Content" from the trigger step and save it into a specified Azure Storage container. The diagram visually represents the logic: for every new email with an attachment, grab each attachment and save it as a file in Blob Storage.

Terraform Code (main.tf)
This Terraform code provisions the Logic App workflow and the required API connections. Note that the first time this is deployed, you must authorize the API connections in the Azure portal.

Terraform

# main.tf
# This Terraform provisions an Azure Logic App workflow.

provider "azurerm" {
  features {}
}

variable "prefix" {
  default = "logicapp"
}

variable "location" {
  default = "East US"
}

# 1. Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = "${var.prefix}-rg"
  location = var.location
}

# 2. Create the storage account that the Logic App will write to
resource "azurerm_storage_account" "storage" {
  name                     = "${var.prefix}storage"
  resource_group_name      = azurerm_resource_group.rg.name
  location                 = azurerm_resource_group.rg.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
}

resource "azurerm_storage_container" "attachments" {
  name                  = "email-attachments"
  storage_account_name  = azurerm_storage_account.storage.name
  container_access_type = "private"
}

# 3. Create the Logic App Workflow
resource "azurerm_logic_app_workflow" "workflow" {
  name                = "${var.prefix}-workflow"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  # The workflow definition is defined in a JSON format.
  # This can be exported from a Logic App designed in the portal.
  parameters = {
    "$connections" = jsonencode({
      "office365" = {
        "connectionId"   = azurerm_logic_app_connection.office365.id,
        "connectionName" = "office365",
        "id"             = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/providers/Microsoft.Web/locations/${azurerm_resource_group.rg.location}/managedApis/office365"
      },
      "azureblob" = {
        "connectionId"   = azurerm_logic_app_connection.azureblob.id,
        "connectionName" = "azureblob",
        "id"             = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/providers/Microsoft.Web/locations/${azurerm_resource_group.rg.location}/managedApis/azureblob"
      }
    })
  }
}

data "azurerm_client_config" "current" {}

# 4. Create managed API connections for Office 365 and Blob Storage
resource "azurerm_logic_app_connection" "office365" {
  name                = "office365"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  managed_api_id      = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/providers/Microsoft.Web/locations/${azurerm_resource_group.rg.location}/managedApis/office365"
  display_name        = "Office365"
}

resource "azurerm_logic_app_connection" "azureblob" {
  name                = "azureblob"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  managed_api_id      = "/subscriptions/${data.azurerm_client_config.current.subscription_id}/providers/Microsoft.Web/locations/${azurerm_resource_group.rg.location}/managedApis/azureblob"
  display_name        = "AzureBlob"
}

_----------

Of course. Here are detailed scenarios designed for learning directly through the cloud console, CLI, or SDKs, without using Terraform. The focus is on the manual steps and understanding the services interactively.

Scenario 19: Deploying a Serverless Application with AWS SAM CLI 🚀
Title: Deploying a Serverless Application with AWS SAM CLI

Description: Learn to build, package, and deploy a complete serverless application (API Gateway, Lambda, DynamoDB) using the AWS Serverless Application Model (SAM) command-line interface, a specialized tool for serverless development.

User Prompt: "Show me the standard AWS-native way to develop and deploy a serverless API."

Services Involved:

API Gateway

Lambda

DynamoDB

CloudFormation (used by SAM behind the scenes)

Level: Intermediate

Estimated Time: 40 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a developer on their local machine using the AWS SAM CLI. The developer works on a template.yaml file and a folder with Lambda function code (e.g., hello_world/app.py). The sam build command is shown processing these files. The sam deploy command is then shown pushing the packaged application to AWS CloudFormation. CloudFormation reads the instructions and provisions the required resources: an API Gateway, a Lambda Function, and a DynamoDB Table, along with the necessary IAM Roles.

Learning Steps (Manual/Programmatic)
This scenario focuses on using the SAM CLI, not the console, for deployment.

Prerequisites: Install the AWS CLI and the AWS SAM CLI on your local machine and configure your AWS credentials.

Initialize a SAM Application: Open your terminal and run sam init. Choose an AWS Quick Start Template (e.g., "Hello World Example") and a runtime (e.g., Python). This creates a project directory with a pre-built template.yaml and function code.

Explore the Template: Open the generated template.yaml file. Observe how it defines resources like AWS::Serverless::Function and AWS::Serverless::Api. This is a simplified abstraction over standard CloudFormation. The template defines the API endpoint, the Lambda function handler, and an API event that triggers the function.

Add a DynamoDB Table: Modify the template.yaml file to include a DynamoDB table resource. Add an AWS::Serverless::SimpleTable resource and give it a name. Update the Lambda function's resource definition to include a policy that grants it read/write access to this new table.

Update Function Code: Modify the Python code inside the function's directory (hello_world/app.py). Add code using the boto3 SDK to interact with the DynamoDB table (e.g., write the request IP to the table). Pass the table name to the function using an environment variable defined in the template.yaml.

Build the Application: In your terminal, navigate to the project root and run the command sam build. This command downloads dependencies, compiles your code, and prepares the application for packaging.

Deploy to AWS: Run the command sam deploy --guided. SAM will prompt you for a stack name, AWS region, and other parameters. Confirm the changes, and SAM will package your application, upload it to S3, and execute the CloudFormation deployment to create all the resources in your AWS account.

Test the Endpoint: Once deployment is complete, SAM will output the API endpoint URL. Access this URL in a browser or with a tool like curl. Then, go to the AWS Management Console, navigate to DynamoDB, and verify that a new item was written to your table.

Scenario 20: Training a Custom Vision AI Model via the Azure Portal 🖼️
Title: Training a Custom Vision AI Model via the Azure Portal

Description: Use the Azure Custom Vision web portal to upload and tag images, train a machine learning model to recognize specific objects, and test its performance without writing any code.

User Prompt: "I want to build an AI that can tell the difference between a cat and a dog from a photo."

Services Involved:

Azure Cognitive Services (Custom Vision)

Level: Beginner

Estimated Time: 25 minutes

Cost Level: Low (Free tier available)

Architecture Diagram (PNG) Description
The diagram is a simple flow chart. It starts with a User who has a Collection of Images (e.g., photos of cats and dogs). The user interacts with the Custom Vision Portal (a web UI). An arrow shows the user uploading images to the portal. Inside the portal, the user is shown tagging the images (drawing boxes and labeling them "cat" or "dog"). Next, the user clicks a "Train" button. This action triggers the Custom Vision AI Service in Azure to train a model. After training, the portal displays Model Performance Metrics (like Precision and Recall). Finally, the user uses the "Quick Test" feature in the portal to upload a new image, and the model returns a prediction with a confidence score.

Learning Steps (Manual/Programmatic)
Create a Custom Vision Resource: In the Azure Portal, search for and create a "Custom Vision" resource. Choose the "Both" option for training and prediction. Select a pricing tier (the F0 Free tier is sufficient).

Launch the Portal: Once the resource is created, navigate to the Custom Vision web portal at www.customvision.ai and sign in.

Create a New Project: Inside the portal, create a new project. Give it a name (e.g., "Cats vs. Dogs"). Select the Custom Vision resource you just created. For Project Type, choose "Object Detection". For Domains, select "General [A2]".

Upload and Tag Images: Go to the "Training Images" tab. Click "Add images" and upload a set of photos containing cats and dogs. Click on the first image to open the tagging interface. Drag a box around the first animal and type the tag "cat" or "dog". Repeat this for every animal in every image. Aim for at least 15 images of each tag.

Train the Model: Once your images are tagged, click the green "Train" button in the top right. Choose a training budget (e.g., 1 hour) for Quick Training. The service will now use your images and tags to train a custom ML model.

Evaluate Performance: After training completes, the "Performance" tab will activate. Observe the key metrics: Precision (When it predicts a tag, how often is it correct?) and Recall (Of all the actual tags, how many did it find?). This helps you understand how good your model is.

Test the Model: Go to the "Quick Test" tab. Click "Browse local files" and upload a new image of a cat or dog that the model has never seen before. The model will analyze the image and draw a box around the object it finds, along with the predicted tag and a confidence score. This gives you a real-world test of your AI's capability.

Scenario 21: Exploring Public Datasets with BigQuery UI 🔍
Title: Exploring Public Datasets with BigQuery UI

Description: Learn to use the Google BigQuery web interface to explore massive public datasets, write standard SQL queries to analyze data, and visualize the results without any setup or data loading.

User Prompt: "Let me analyze real-world data, like all the Reddit comments or Wikipedia page views."

Services Involved:

BigQuery

BigQuery Public Datasets

Level: Beginner

Estimated Time: 20 minutes

Cost Level: Low (within the free tier sandbox)

Architecture Diagram (PNG) Description
The diagram is focused on a single service. On the left, a large database icon is labeled BigQuery Public Datasets, containing numerous well-known datasets (Hacker News, USA Names, etc.). In the center is the BigQuery Web UI. An analyst is shown interacting with the UI. The UI is split into three main parts: the Navigation Pane on the left where the user can browse datasets and tables, the Query Editor at the top where the user writes SQL, and the Query Results pane at the bottom where the output is displayed. An arrow points from the Query Editor to the Public Datasets, representing a query being run. Another arrow points from the Public Datasets to the Query Results pane.

Learning Steps (Manual/Programmatic)
Open the BigQuery Console: Navigate to the Google Cloud Console and select "BigQuery". You can use the BigQuery Sandbox without providing a credit card.

Add a Public Dataset: In the navigation pane on the left, click the "+ ADD" button. Select "Public datasets" from the options. A marketplace will appear. Search for a dataset, for example, "USA Names", and click "View Dataset". This will add the bigquery-public-data.usa_names dataset to your navigation pane.

Explore the Schema: In the navigation pane, expand the usa_names dataset. You will see a table inside called usa_1910_current. Click on this table. In the main window, the "Schema" tab will appear, showing you all the column names (name, state, year, number) and their data types.

Write a Simple Query: In the Query Editor, type a basic SQL query to see the most popular names in a specific state and year. For example:

SQL

SELECT
  name,
  number
FROM
  `bigquery-public-data.usa_names.usa_1910_current`
WHERE
  state = 'CA' AND year = 2010
ORDER BY
  number DESC
LIMIT 10;
Run the Query: Click the "RUN" button. Observe the query results that appear in the bottom pane. Notice the "Job information" tab, which tells you how much data was processed and how long the query took to run. This demonstrates BigQuery's power to scan huge amounts of data quickly.

Explore Another Query: Write a slightly more complex query to find the trend of a specific name over time.

SQL

SELECT
  year,
  number
FROM
  `bigquery-public-data.usa_names.usa_1910_current`
WHERE
  name = 'Michael'
ORDER BY
  year;
Visualize Results: After running the query, click the "Explore Data" button and choose "Explore with Looker Studio". This will automatically open a new tab with a basic chart of your query results, showing you how to quickly move from raw data to a visualization.


-------------

Yes, the scenarios cover AWS, Azure, and GCP. Here are three more detailed scenarios, one for each major cloud provider, to ensure comprehensive coverage.

Scenario 22: AWS S3 Cross-Region Replication for Disaster Recovery 🛟
Title: AWS S3 Cross-Region Replication for Disaster Recovery

Description: Configure Amazon S3 to automatically and asynchronously replicate objects from a bucket in one AWS region to a bucket in another region to ensure data durability and availability in case of a regional outage.

User Prompt: "I need to make sure my critical data in S3 survives if an entire AWS region goes down."

Services Involved:

S3 (Simple Storage Service)

IAM (Identity and Access Management)

Level: Intermediate

Estimated Time: 20 minutes

Cost Level: Low (plus data transfer costs)

Architecture Diagram (PNG) Description
The diagram shows two AWS regions, for example, us-east-1 and us-west-2, represented by two large boxes. Inside the us-east-1 box, there is an S3 Bucket labeled "Primary Bucket". A user or application is shown writing an object (a file icon) to this bucket. A dotted arrow labeled "Replication Rule" points from the primary bucket in us-east-1 to another S3 Bucket in the us-west-2 box, labeled "Replica Bucket". The same object icon appears in the replica bucket shortly after, indicating successful replication. An IAM Role is depicted with an arrow pointing to both buckets, signifying that it has permissions to read from the source and write to the destination.

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform configures S3 Cross-Region Replication (CRR).

provider "aws" {
  alias  = "primary"
  region = "us-east-1"
}

provider "aws" {
  alias  = "replica"
  region = "us-west-2"
}

# 1. Create the primary S3 bucket in the primary region
resource "aws_s3_bucket" "primary_bucket" {
  provider = aws.primary
  bucket   = "my-primary-app-bucket-use1" # Replace with a unique name

  # Enable versioning, which is a prerequisite for replication
  versioning {
    enabled = true
  }
}

# 2. Create the replica S3 bucket in the replica region
resource "aws_s3_bucket" "replica_bucket" {
  provider = aws.replica
  bucket   = "my-replica-app-bucket-usw2" # Replace with a unique name

  versioning {
    enabled = true
  }
}

# 3. Create the IAM role that S3 will assume to perform replication
resource "aws_iam_role" "replication_role" {
  name = "s3-replication-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action    = "sts:AssumeRole",
        Effect    = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        }
      }
    ]
  })
}

# 4. Attach a policy to the role granting permissions for replication
resource "aws_iam_policy" "replication_policy" {
  name = "s3-replication-policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:GetReplicationConfiguration",
          "s3:ListBucket"
        ],
        Effect   = "Allow",
        Resource = [aws_s3_bucket.primary_bucket.arn]
      },
      {
        Action = [
          "s3:GetObjectVersion",
          "s3:GetObjectVersionAcl"
        ],
        Effect   = "Allow",
        Resource = ["${aws_s3_bucket.primary_bucket.arn}/*"]
      },
      {
        Action = [
          "s3:ReplicateObject",
          "s3:ReplicateDelete"
        ],
        Effect   = "Allow",
        Resource = "${aws_s3_bucket.replica_bucket.arn}/*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "replication_attachment" {
  role       = aws_iam_role.replication_role.name
  policy_arn = aws_iam_policy.replication_policy.arn
}

# 5. Configure the replication rule on the primary bucket
resource "aws_s3_bucket_replication_configuration" "replication_config" {
  provider = aws.primary
  bucket   = aws_s3_bucket.primary_bucket.id
  role     = aws_iam_role.replication_role.arn

  rule {
    id = "primary-to-replica-rule"
    status = "Enabled"

    destination {
      bucket = aws_s3_bucket.replica_bucket.arn
    }
  }

  depends_on = [aws_iam_role_policy_attachment.replication_attachment]
}
Scenario 23: Centralized Logging and Security Analysis with Azure Sentinel 🕵️
Title: Centralized Logging and Security Analysis with Azure Sentinel

Description: Set up Azure Sentinel to collect, detect, investigate, and respond to security threats across your Azure environment by ingesting data from various sources like Azure Activity Log and virtual machines.

User Prompt: "I need a single place to see all security alerts and logs from my Azure resources."

Services Involved:

Azure Sentinel (Cloud-native SIEM)

Log Analytics Workspace (The foundation for Sentinel)

Data Connectors (e.g., Azure Activity, Security Center)

Level: Intermediate

Estimated Time: 25 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows various Azure Resources on the left (Virtual Machines, Azure AD, Azure Firewall). Arrows from each of these resources point towards a central Log Analytics Workspace, indicating that logs and events are being sent here. Layered on top of the Log Analytics Workspace is the Azure Sentinel service. Sentinel is shown with several functional blocks: "Data Collection", "Detection (Analytics Rules)", "Investigation", and "Automation (Playbooks)". An arrow flows from the Log Analytics Workspace into Sentinel's data collection. A Security Analyst is shown interacting with the Sentinel dashboard to view incidents, hunt for threats, and trigger automated responses (Playbooks).

Terraform Code (main.tf)
Terraform

# main.tf
# This Terraform provisions an Azure Sentinel instance and connects it to Azure Activity logs.

provider "azurerm" {
  features {}
}

variable "prefix" {
  default = "sentinel"
}
variable "location" {
  default = "East US"
}

# 1. Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = "${var.prefix}-rg"
  location = var.location
}

# 2. Create a Log Analytics Workspace, which is the foundation for Sentinel
resource "azurerm_log_analytics_workspace" "workspace" {
  name                = "${var.prefix}-workspace"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  sku                 = "PerGB2018"
  retention_in_days   = 30
}

# 3. Onboard Azure Sentinel onto the Log Analytics Workspace
resource "azurerm_sentinel_log_analytics_workspace_onboarding" "onboarding" {
  workspace_id = azurerm_log_analytics_workspace.workspace.id
}

# 4. Enable a Data Connector to start ingesting logs
# This example connects the Azure Activity Log to Sentinel.
resource "azurerm_sentinel_data_connector_azure_activity" "activity_connector" {
  name                       = "AzureActivity"
  log_analytics_workspace_id = azurerm_sentinel_log_analytics_workspace_onboarding.onboarding.workspace_id
}

# 5. Create an Analytics Rule to detect suspicious activity
# This example rule detects when a custom security rule is created on a Network Security Group.
resource "azurerm_sentinel_alert_rule_scheduled" "suspicious_nsg_rule" {
  name                       = "SuspiciousNSGRuleCreation"
  log_analytics_workspace_id = azurerm_sentinel_log_analytics_workspace_onboarding.onboarding.workspace_id
  display_name               = "Creation of suspicious NSG rule"
  severity                   = "Medium"
  enabled                    = true

  query = <<-QUERY
    AzureActivity
    | where OperationNameValue == "MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/SECURITYRULES/WRITE"
    | where ActivityStatusValue == "Success"
    | extend securityRule = properties_d.requestbody.properties
    | where securityRule.access == "Allow"
    | where securityRule.direction == "Inbound"
    | where securityRule.protocol == "Tcp"
    | where securityRule.destinationPortRange == "3389" or securityRule.destinationPortRange == "22"
    | where securityRule.sourceAddressPrefix == "*" or securityRule.sourceAddressPrefix == "0.0.0.0" or securityRule.sourceAddressPrefix == "any" or securityRule.sourceAddressPrefix == "internet"
  QUERY

  query_frequency = "PT1H" # Run query every hour
  query_period    = "PT1H" # Look back over the last hour
  trigger_operator = "GreaterThan"
  trigger_threshold = 0
}
Scenario 24: Running Batch HPC Jobs with Google Cloud Batch 🧑‍💻
Title: Running Batch HPC Jobs with Google Cloud Batch

Description: Define and run a large-scale, high-performance computing (HPC) batch job on managed infrastructure that automatically provisions and de-provisions resources as needed.

User Prompt: "I need to run a 10,000-core genomics simulation job overnight without managing my own cluster."

Services Involved:

Cloud Batch

Compute Engine (managed by Batch)

Cloud Storage (for input/output data)

Level: Advanced

Estimated Time: 30 minutes

Cost Level: High (while the job is running)

Architecture Diagram (PNG) Description
The diagram shows a User or a Scheduler submitting a Job Definition (JSON) to the Cloud Batch API. The job definition specifies the container image to run, the number of tasks, CPU/memory requirements, and input/output data locations. The Cloud Batch service receives this job and automatically provisions a temporary pool of Compute Engine VMs. The VMs pull input data from a Cloud Storage bucket, run their assigned tasks in parallel, and write their output back to another Cloud Storage bucket. Once the job is complete, the Cloud Batch service automatically terminates all the Compute Engine VMs, ensuring the user only pays for the compute time used.

Terraform Code (main.tf)
This Terraform code defines a Cloud Batch job. The job itself is simple (prints a message), but the configuration demonstrates how to set up a parallel task for a large-scale workload.

Terraform

# main.tf
# This Terraform defines and creates a job in Google Cloud Batch.

provider "google" {
  project = "your-gcp-project-id" # Replace with your GCP project ID
  region  = "us-central1"
}

variable "project_id" {
  description = "The GCP Project ID."
}
variable "region" {
  description = "The GCP region to run the job."
  default     = "us-central1"
}

# 1. Enable the Batch API
resource "google_project_service" "batch_api" {
  project            = var.project_id
  service            = "batch.googleapis.com"
  disable_on_destroy = false
}

# 2. Define the Cloud Batch Job
resource "google_batch_job" "hpc_job" {
  project  = var.project_id
  region   = var.region
  name     = "hpc-simulation-job"
  
  # Define what the job is made of
  task_groups {
    task_count          = 1000 # Run 1,000 tasks in parallel
    parallelism         = 1000 # Allow all 1,000 to run at once if resources are available
    
    # Define the spec for each individual task
    task_spec {
      # Define the runnable(s) for the task
      runnables {
        # This example uses a simple script in a container.
        # A real job would use a custom container with scientific software.
        container {
          image_uri = "gcr.io/google-containers/busybox"
          entrypoint = "/bin/sh"
          commands = [
            "-c",
            "echo 'Hello from task ${BATCH_TASK_INDEX}!'"
          ]
        }
      }

      # Define the compute resources required for each task
      compute_resource {
        cpu_milli = 1000 # 1 vCPU
        memory_mib = 512 # 512 MiB
      }

      max_run_duration = "3600s" # 1 hour timeout
    }
  }

  # Define the allocation policy for the VMs that will be created
  allocation_policy {
    instances {
      policy {
        # Use standard 'e2-standard-2' machines for this job
        machine_type = "e2-standard-2"
      }
    }
  }

  # Define what to log
  logs_policy {
    destination = "CLOUD_LOGGING"
  }

  depends_on = [google_project_service.batch_api]
}


-----------

Of course. Here are three very common and important architectures, one for each major cloud, designed for learning through the console without Terraform.

Scenario 25: AWS Dynamic Image Resizing and Caching 🖼️
Title: Dynamic Image Resizing and Caching

Description: Build a cost-effective, serverless solution that automatically resizes an image on-the-fly when requested for the first time and caches the result globally for all subsequent requests.

User Prompt: "I need a way to create different image sizes (like thumbnails) automatically without pre-processing everything."

Services Involved:

S3 (Simple Storage Service)

API Gateway

Lambda

CloudFront (Content Delivery Network)

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a user requesting a specific image size, like .../images/123_200x200.jpg, from a CloudFront distribution. CloudFront first checks its cache.

If the image is not in the cache (a "cache miss"), CloudFront forwards the request to its origin, which is an API Gateway. The API Gateway triggers a Lambda function, passing the image name and dimensions from the URL. The Lambda function then fetches the original, high-resolution image from a private S3 Bucket (e.g., 123.jpg), resizes it in memory to 200x200, and returns the resized image back to CloudFront. CloudFront then caches this new resized image and serves it to the user.

If the image is in the cache (a "cache hit" on subsequent requests), CloudFront serves the resized image directly to the user from the edge location, providing a very fast response and never invoking the backend Lambda function.

Learning Steps (Manual/Programmatic)
Create S3 Buckets: In the AWS Management Console, navigate to S3. Create two S3 buckets: one for your original high-resolution images (e.g., my-app-originals) and another for the resized images (e.g., my-app-resized-cache). Keep them private.

Create IAM Role: Go to IAM and create a role for your Lambda function. Attach the AWSLambdaBasicExecutionRole policy for logging. Create a custom inline policy that grants s3:GetObject permission on the originals bucket and s3:PutObject permission on the resized cache bucket.

Create Lambda Function: Go to Lambda and create a new function (e.g., using Python or Node.js). Attach the IAM role you created. In the function's code, write the logic to:

Parse the image key and dimensions from the incoming event.

Check if the resized image already exists in the resized-cache bucket. If so, redirect to it.

If not, download the original image from the originals bucket into the Lambda's temporary storage.

Use an image processing library (like Pillow for Python) to resize the image.

Upload the resized image to the resized-cache bucket.

Return a redirect response pointing to the newly created resized image.

Create API Gateway: Go to API Gateway and create a new REST API. Create a resource path like /images/{key}. Create a GET method on this resource and configure it to use a Lambda Proxy integration, pointing to the function you just created. Deploy the API to a stage.

Create CloudFront Distribution: Go to CloudFront and create a new distribution. For the origin, select the API Gateway endpoint you just deployed. Configure the "Cache key and origin requests" settings to forward the Host header and whitelist the s3-region header for the S3 redirect to work correctly.

Test the Flow: Upload a large JPG file to your originals bucket. Construct the URL using your CloudFront domain and the image key with desired dimensions (e.g., https://<cloudfront_id>.cloudfront.net/images/my-photo_400x400.jpg). The first time you access it, it will be slow as the Lambda function runs. Refresh the page, and it will load instantly from the CloudFront cache.

Scenario 26: Azure Secure Access to PaaS Services with Private Endpoints 🔒
Title: Secure Access to PaaS Services with Private Endpoints

Description: Learn how to ensure that traffic from a Virtual Machine to an Azure PaaS service (like Azure SQL Database) never leaves the Microsoft backbone network, enhancing security by eliminating public internet exposure.

User Prompt: "How do I let my private virtual machine connect to my SQL database without giving the database a public IP address?"

Services Involved:

Virtual Network (VNet)

Virtual Machine

Azure SQL Database

Private Endpoint

Private DNS Zone

Level: Advanced

Estimated Time: 40 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an Azure Virtual Network (VNet). Inside the VNet, a Virtual Machine resides in a subnet. Separately, an Azure SQL Database is shown. Crucially, the SQL Database has its public network access disabled (firewall set to "Deny").

A Private Endpoint resource is created for the SQL Database. This Private Endpoint projects a Network Interface (NIC) directly into the VNet's subnet. This NIC has a private IP address from the VNet's address space. An arrow points from the VM to this private IP address.

To handle name resolution, a Private DNS Zone for privatelink.database.windows.net is created and linked to the VNet. This zone contains an A record that maps the database's standard public name (e.g., mydb.database.windows.net) to the private IP address of the Private Endpoint's NIC. When the VM tries to connect to mydb.database.windows.net, the DNS lookup resolves to the internal private IP, and traffic flows securely over the private network.

Learning Steps (Manual/Programmatic)
Set up the Network: In the Azure Portal, create a Virtual Network (VNet) with at least one subnet.

Deploy a Virtual Machine: Create a Windows or Linux Virtual Machine and place it inside the subnet you created. This will be your client machine.

Create an Azure SQL Database: Create an Azure SQL Database and a logical server. During creation or afterward, go to the server's "Networking" settings. Under "Public access," select "Disable". This is a critical step.

Create the Private Endpoint: In the SQL Database's "Networking" tab, select "Private access" and click "+ Create a private endpoint".

Place the private endpoint in the same VNet and subnet as your VM.

For the "Resource" step, ensure it's pointing to your SQL server and the "Target sub-resource" is sqlServer.

In the "Configuration" step, choose "Yes" for "Integrate with private DNS zone". This automatically creates the Private DNS Zone and the required A record.

Verify the Connection: RDP or SSH into your Virtual Machine. Use a command-line tool to verify the DNS resolution. For example, run nslookup your-sql-server-name.database.windows.net. You should see it resolve to a private IP address (e.g., ********), not a public one.

Test Database Access: Using a tool like SQL Server Management Studio (SSMS) or sqlcmd, attempt to connect to your SQL server from the VM using its standard public name. The connection will succeed because all traffic is being routed privately within the VNet. If you were to try the same connection from your own laptop over the internet, it would fail.

Scenario 27: CI/CD Pipeline for GKE with Cloud Build ☸️
Title: CI/CD Pipeline for GKE with Cloud Build

Description: Create a fully automated pipeline that takes source code from a repository, builds a container image, pushes it to a registry, and deploys it to a Google Kubernetes Engine (GKE) cluster.

User Prompt: "I want to set up a professional CI/CD pipeline for my microservice running on Kubernetes."

Services Involved:

Google Kubernetes Engine (GKE)

Cloud Build

Artifact Registry (for container images)

Cloud Source Repositories (or GitHub/Bitbucket)

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a developer committing code to a Cloud Source Repository. This commit action automatically triggers Cloud Build. Cloud Build is shown as a pipeline defined by a cloudbuild.yaml file in the repository.

The pipeline has three main stages:

Build: Cloud Build uses the Dockerfile in the repo to build a new container image.

Push: The newly built image is pushed to the Artifact Registry.

Deploy: Cloud Build uses kubectl commands to apply a Kubernetes manifest file (e.g., deployment.yaml) to the GKE Cluster. This step updates the GKE deployment to use the new container image version from the Artifact Registry.

The final result is a new Pod being rolled out in the GKE cluster, running the updated application code.

Learning Steps (Manual/Programmatic)
Prepare a Sample Application: On your local machine, create a simple web application (e.g., in Node.js or Python), a Dockerfile to containerize it, and a Kubernetes deployment.yaml manifest file.

Create a GKE Cluster: In the GCP Console, navigate to Kubernetes Engine and create a standard GKE cluster.

Create an Artifact Registry: Go to Artifact Registry and create a new Docker repository to store your container images.

Set up a Code Repository: Go to Cloud Source Repositories, create a new repository, and push your sample application code (including the Dockerfile and deployment manifest) to it.

Grant Permissions: Go to Cloud Build -> Settings. Enable the "Kubernetes Engine" service account permission. This allows Cloud Build to have the necessary IAM permissions to deploy to your GKE cluster.

Create the Build Configuration File: In your application's root directory, create a file named cloudbuild.yaml. This file defines the steps for your pipeline:

YAML

steps:
# 1. Build the container image
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '-t', 'us-central1-docker.pkg.dev/$PROJECT_ID/my-repo/my-app:$SHORT_SHA', '.']
# 2. Push the image to Artifact Registry
- name: 'gcr.io/cloud-builders/docker'
  args: ['push', 'us-central1-docker.pkg.dev/$PROJECT_ID/my-repo/my-app:$SHORT_SHA']
# 3. Deploy to GKE
- name: 'gcr.io/cloud-builders/gke-deploy'
  args:
  - 'run'
  - '--image=us-central1-docker.pkg.dev/$PROJECT_ID/my-repo/my-app:$SHORT_SHA'
  - '--location=us-central1'
  - '--cluster=your-gke-cluster-name'
  - '--deployment=my-app-deployment' # Name from your deployment.yaml
images:
- 'us-central1-docker.pkg.dev/$PROJECT_ID/my-repo/my-app:$SHORT_SHA'
Commit and push this file to your repository.

Create the Build Trigger: In the Cloud Build console, go to Triggers. Create a new trigger. Select your Cloud Source Repository. For the event, choose "Push to a branch". For the configuration, select the cloudbuild.yaml file. Save the trigger.

Test the Pipeline: Make a small change to your application code and push the commit to your repository's main branch. Go to the Cloud Build history page. You will see a new build running automatically. Watch as it completes the Build, Push, and Deploy steps. Finally, go to the GKE console, view your workloads, and verify that your deployment has been updated with a new pod running the latest code.



-----------------

Scenario 28: AWS Federated Authentication with Amazon Cognito 🧑‍🤝‍🧑
Title: Federated Authentication with Amazon Cognito

Description: Learn how to add user sign-up, sign-in, and access control to your web application by integrating with Amazon Cognito, allowing users to authenticate with social identity providers like Google or Facebook.

User Prompt: "I need to add a login system to my app so users can sign in with their Google account."

Services Involved:

Amazon Cognito (User Pools & Identity Pools)

IAM (Identity and Access Management)

A simple static web application (hosted on S3 for the demo)

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a user interacting with a Web Application. When the user clicks "Login," the application redirects them to the Amazon Cognito Hosted UI. This UI displays options like "Sign in with Google" or "Sign in with Facebook." The user authenticates with their chosen provider (e.g., Google). After successful authentication, Google returns a token to Cognito. Cognito then creates a user profile in its User Pool and provides the web application with a JSON Web Token (JWT). The application can then use this JWT to exchange it for temporary AWS credentials via a Cognito Identity Pool. These temporary credentials allow the web app to securely access other AWS services, like a private S3 bucket or an API Gateway endpoint, on behalf of the logged-in user.

Learning Steps (Manual/Programmatic)
Create a Cognito User Pool: In the AWS Management Console, navigate to Amazon Cognito. Click "Create user pool." Choose "Federated identity providers" and select providers like Google or Facebook. You will need to provide a Client ID and Client Secret, which you get by creating an "OAuth 2.0 Client ID" in the Google Cloud Console for your application.

Configure the App Client: In your User Pool settings, go to the "App integration" tab. Create a new "App client." Select "Public client" and generate a client secret. Note the Client ID.

Configure the Hosted UI: Under "App integration," find the "App client" you just created and click "Edit hosted UI." Add an "Allowed callback URL" (e.g., http://localhost:8000 for local testing or your web app's URL). This tells Cognito where to send the user after they successfully log in.

Create a Cognito Identity Pool: Navigate to "Federated identities" in the Cognito console and create a new Identity Pool. Enable access for unauthenticated identities. Under "Authentication providers," select the "User Pool" tab and enter the User Pool ID and App Client ID you created earlier.

Review IAM Roles: When you create the Identity Pool, Cognito automatically creates two IAM roles: one for authenticated users and one for unauthenticated users. Examine the "authenticated" role. This is the role your users will assume after logging in. You can attach policies to this role to grant them specific permissions (e.g., access to a specific S3 bucket).

Integrate with a Sample App: Download or create a simple single-page JavaScript application. Use a library like the "Amazon Cognito Identity SDK" or construct the login URL manually. The URL will direct users to your Cognito Hosted UI.

Test the Login Flow: Open your web app. Click the login button. You should be redirected to the Cognito Hosted UI. Sign in with the Google account you configured. After a successful login, Cognito will redirect you back to your app's callback URL with an authorization code in the URL. Your application can then exchange this code for user tokens, proving the user is authenticated.

Scenario 29: Azure Hybrid Cloud Networking with VPN Gateway 🏢
Title: Azure Hybrid Cloud Networking with VPN Gateway

Description: Establish a secure, site-to-site VPN tunnel connecting your on-premises office network to an Azure Virtual Network (VNet), allowing resources in both locations to communicate as if they were on the same network.

User Prompt: "I need my servers in Azure to securely access a database in my office."

Services Involved:

Virtual Network (VNet)

VPN Gateway

Local Network Gateway

Connection

Level: Advanced

Estimated Time: 60 minutes

Cost Level: High

Architecture Diagram (PNG) Description
The diagram shows two main locations. On the right is an Azure Virtual Network (VNet) containing subnets and virtual machines. On the left is an On-Premises Network representing a physical office with its own servers.

In Azure, a special subnet called the GatewaySubnet is created within the VNet. A VPN Gateway resource is deployed into this subnet. In the on-premises network, a physical VPN Device (like a router or firewall) is shown with a public IP address.

To represent the on-premises network in Azure, a Local Network Gateway resource is created. This resource holds the public IP address of the on-premises VPN device and the office's internal IP address ranges. Finally, a Connection resource is created that links the Azure VPN Gateway to the Local Network Gateway, establishing the encrypted site-to-site IPsec VPN tunnel over the public internet.

Learning Steps (Manual/Programmatic)
Create the Virtual Network: In the Azure Portal, create a Virtual Network (VNet) with an address space (e.g., ********/16). Create a subnet for your workloads (e.g., ********/24).

Create the Gateway Subnet: This is a critical step. Inside your VNet, create a special subnet and name it exactly GatewaySubnet. Azure requires this specific name. A /27 address range is typically sufficient.

Create the VPN Gateway: Search for "Virtual network gateway" and create one.

Select "VPN" for the gateway type and "Route-based" for the VPN type.

Choose a SKU (e.g., VpnGw1). Higher SKUs provide more throughput.

Assign it to your VNet. The gateway will be automatically placed in the GatewaySubnet.

Create a new public IP address for the gateway. This step can take up to 45 minutes to provision.

Create the Local Network Gateway: This resource acts as a placeholder for your on-premises network. Search for "Local network gateway" and create one.

Enter the public IP address of your office's VPN device/firewall.

In "Address space," enter the internal IP address ranges of your on-premises network (e.g., ***********/24).

Configure Your On-Premises VPN Device: This step happens outside of Azure. You need to configure your physical office router or firewall to create a matching VPN tunnel. You'll need the Azure VPN Gateway's public IP address, the VNet's address space, and a pre-shared key (a secret password for the tunnel).

Create the Connection: Navigate back to your created VPN Gateway in Azure. Go to the "Connections" blade and click "+ Add".

Select "Site-to-site (IPsec)" as the connection type.

Choose the Local Network Gateway you created in step 4.

Enter the same pre-shared key (PSK) that you configured on your on-premises device.

Verify the Tunnel: After a few minutes, the status of the connection in the portal should change to "Connected". You can now test connectivity by trying to ping a private IP address in Azure from an on-premises machine, or vice-versa (ensure firewalls allow ICMP traffic).

Scenario 30: GCP Autoscaling a Web Application ⚖️
Title: Autoscaling a Web Application with Managed Instance Groups

Description: Deploy a resilient web application that automatically scales the number of virtual machines up or down based on real-time CPU utilization, ensuring performance during traffic spikes and saving costs during quiet periods.

User Prompt: "My website is slow during peak hours. How can I make it handle more traffic automatically?"

Services Involved:

Compute Engine

Instance Template

Managed Instance Group (MIG)

Cloud Load Balancing

Cloud Monitoring

Level: Intermediate

Estimated Time: 40 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows incoming user traffic hitting a Cloud Load Balancer. The load balancer distributes this traffic to a Managed Instance Group (MIG). The MIG is shown as a box containing several identical Compute Engine VM instances. An Instance Template is depicted as a blueprint that is used by the MIG to create these identical VMs.

An Autoscaler is attached to the MIG. It is shown monitoring metrics (like CPU utilization) from the VMs via Cloud Monitoring. The autoscaler has rules defined, such as "Add VMs if CPU > 60%" and "Remove VMs if CPU < 20%". When a condition is met, the autoscaler instructs the MIG to either create new VMs using the instance template or delete existing ones.

Learning Steps (Manual/Programmatic)
Create an Instance Template: In the GCP Console, navigate to Compute Engine -> Instance templates. Create a new template.

Choose a machine type (e.g., e2-medium).

Select a boot disk image (e.g., Debian).

Under "Firewall," allow HTTP traffic.

In the "Startup script" field, enter a simple script to install and run a web server, like Apache2. For example:

Bash

#!/bin/bash
apt-get update
apt-get install -y apache2
echo "Hello from $(hostname)" > /var/www/html/index.html
Create a Managed Instance Group (MIG): Go to Instance groups and create a new group.

Select the instance template you just created.

Set the "Location" to "Single zone."

Under "Autoscaling," configure the policy. Set the "Mode" to "On: add and remove instances to the group."

Set the "Autoscaling metric" to "CPU utilization" and the "Target CPU utilization" to 60 (for 60%).

Set the minimum number of instances to 1 and the maximum to 5.

Define a "Cooldown period" (e.g., 60 seconds) to prevent the autoscaler from reacting too aggressively.

Create a Health Check: Before creating the load balancer, go to Health checks and create one. Configure it to check for an HTTP 200 response on port 80. This tells the load balancer if an instance is healthy.

Create a Load Balancer: Go to Load balancing and create an "HTTP(S) Load Balancer."

For the "Backend configuration," select "Create a backend service." Choose your MIG as the backend and attach the health check you just created.

For the "Frontend configuration," a public IP address and port (80) will be created for you.

Review and Create: Finalize the creation of the load balancer. It can take a few minutes to provision.

Test the Setup: Find the public IP address of your load balancer and access it in a web browser. You should see the "Hello from..." message. Refreshing the page may show different hostnames as the load balancer distributes traffic.

Test Autoscaling (Optional): To simulate load, you would need to use a load testing tool (like Apache Bench) to send a high volume of traffic to the load balancer's IP. As you do this, you can go to the Instance groups page in the console and watch as the number of instances automatically increases from 1 up towards 5 to handle the load. When you stop the test, the instances will automatically scale back down after the cooldown period.



---------------

Of course. Here are three more architecture scenarios focused on console-based learning for AWS, Azure, and GCP.

Scenario 31: AWS Building a Serverless Data Lake with Lake Formation 🔐
Title: Building a Serverless Data Lake with Lake Formation

Description: Learn how to build a secure data lake on S3, manage fine-grained permissions for data catalogs and underlying data, and query it using Athena, all centrally governed by AWS Lake Formation.

User Prompt: "I need to give my data science team read-only access to specific columns in our customer data, not the whole table."

Services Involved:

S3 (Simple Storage Service)

AWS Lake Formation

AWS Glue (Crawlers and Data Catalog)

Amazon Athena

IAM

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a central AWS Lake Formation service governing the entire architecture. A Data Lake Administrator role is shown interacting with Lake Formation. An S3 Bucket is "registered" with Lake Formation as the data lake storage. An AWS Glue Crawler runs under Lake Formation's control, crawling the S3 data and populating the Glue Data Catalog.

Two user roles are shown: a Data Analyst and a Marketing User. The Data Lake Administrator uses the Lake Formation console to grant permissions. The Data Analyst is granted full table access, while the Marketing User is granted access to only specific columns (e.g., product_id, purchase_date) and is denied access to sensitive PII columns (e.g., customer_name, email). Both users then run queries through Amazon Athena. When a query is executed, Athena checks with Lake Formation to see what data that specific user is authorized to see, and the results are filtered accordingly before being returned.

Learning Steps (Manual/Programmatic)
Set Up Lake Formation: In the AWS Console, navigate to AWS Lake Formation. You'll be prompted to create a Data Lake Administrator. Add your own IAM user as an administrator.

Register an S3 Bucket: Go to "Data lake locations" and register an S3 bucket that will hold your raw data. Lake Formation will now manage permissions for this location.

Create a Database: In the Lake Formation console, go to "Databases" and create a new database. This is a logical grouping for your tables within the Glue Data Catalog.

Create a Crawler: Navigate to AWS Glue and create a new crawler. Point it to a specific folder within your registered S3 bucket. Crucially, assign it an IAM role that has been granted permissions by Lake Formation to access the data. Run the crawler to discover the schema and create a table in your database.

Revoke Default Permissions: By default, IAM users have wide access. In Lake Formation, find the IAMAllowedPrincipals group under "Data lake permissions" and revoke its access to your new table. This ensures Lake Formation is now in full control.

Grant Fine-Grained Permissions: Go to "Data lake permissions" and click "Grant".

Select an IAM user or role (e.g., one for a data analyst).

Choose the database and table you created.

Grant permissions. You can select specific columns to include or exclude. For example, grant SELECT access but only on the non-sensitive columns.

Query with Athena: Log in as the IAM user you just granted permissions to. Navigate to Amazon Athena. Run SELECT * FROM your_database.your_table;. You will see that the query results only contain the specific columns you were granted access to, even though you requested all (*). This demonstrates that Lake Formation's permissions are being enforced.

Scenario 32: Azure Geo-Redundant Web App with Azure Front Door 🌐
Title: Deploying a Geo-Redundant Web App with Azure Front Door

Description: Build a highly available, global web application by deploying it to two separate Azure regions and using Azure Front Door to route users to the closest healthy region, providing fast performance and automatic failover.

User Prompt*: "How do I make my web app fast for users in both the US and Europe, and keep it online if one data center fails?"

Services Involved:

Azure Front Door

Azure App Service

Resource Groups

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: High

Architecture Diagram (PNG) Description
The diagram shows users from different parts of the world (e.g., North America and Europe) making requests to a single Azure Front Door endpoint. Front Door is depicted as a global service at the network edge.

Front Door has a "backend pool" that contains two backends. The first backend is an App Service instance running in the East US Azure region. The second backend is another App Service instance running in the West Europe region.

Arrows show that Front Door intelligently routes traffic: the North American user is sent to the East US App Service, while the European user is sent to the West Europe App Service. A health probe is shown periodically checking the status of both App Service instances. A dotted-line arrow illustrates a failover scenario: if the East US region becomes unhealthy, Front Door automatically reroutes all traffic to the healthy West Europe region.

Learning Steps (Manual/Programmatic)
Create Resources in Two Regions: In the Azure Portal, create two separate Resource Groups, one in "East US" and another in "West Europe".

Deploy Primary App Service: In the "East US" resource group, create an App Service Plan and an App Service. Deploy a simple web application to it that displays its location (e.g., "Hello from East US").

Deploy Secondary App Service: Repeat the process in the "West Europe" resource group. Create another App Service Plan and App Service. Deploy the same application, but change the code to display "Hello from West Europe".

Create Azure Front Door: Search for "Front Door" in the portal and create a new Front Door (Standard/Premium tier).

In the "Frontends/domains" step, a default endpoint will be created for you (e.g., my-app.azurefd.net).

In the "Backend pools" step, click "+ Add a backend pool".

Click "+ Add a backend". For the "Backend host type," select "App service" and choose your "East US" app service.

Click "+ Add a backend" again in the same pool and add your "West Europe" app service. Leave the priority and weight the same for both to enable latency-based routing.

Configure Routing Rule: Go to the "Routing rules" step. Add a rule that associates your frontend domain with the backend pool you just created.

Test the Routing: After Front Door is deployed, access its URL (my-app.azurefd.net) from your browser. You should be directed to the App Service geographically closest to you. To test the other region, you can use a web-based VPN or proxy service to make a request from the other continent.

Test Failover: Go to your primary App Service (e.g., the one in East US) and stop it. Wait a few minutes for the Front Door health probes to detect the failure. Now, when you access the Front Door URL (even from the US), you will be automatically redirected to the "Hello from West Europe" page, demonstrating a successful failover.

Scenario 33: GCP Service-to-Service Authentication with a Service Mesh 👮
Title: Service-to-Service Authentication with a Service Mesh

Description: Secure microservices communication within a GKE cluster by deploying a service mesh (Anthos Service Mesh) to automatically enforce mutual TLS (mTLS), ensuring that services can only communicate if they have a valid, verified identity.

User Prompt: "How can I make sure my 'orders' microservice only accepts traffic from my 'frontend' service and nothing else?"

Services Involved:

Google Kubernetes Engine (GKE)

Anthos Service Mesh (managed Istio)

Cloud Monitoring & Cloud Logging

Level: Advanced

Estimated Time: 75 minutes

Cost Level: High (GKE costs + Anthos licensing, if applicable)

Architecture Diagram (PNG) Description
The diagram shows a GKE Cluster. Inside the cluster are several Pods representing different microservices (e.g., frontend, orders, products). Next to each application container within the pods is a second, smaller container labeled Envoy Proxy (Sidecar). These sidecars are injected automatically by the Anthos Service Mesh Control Plane.

An arrow shows a request originating from the frontend pod. The request is first intercepted by the frontend's Envoy proxy. This proxy initiates a secure mTLS handshake with the Envoy proxy of the orders pod. Certificates are exchanged and verified by the Anthos Control Plane. If the identities are valid, the encrypted connection is established. An arrow shows this secure traffic flow between the two sidecar proxies. Another arrow shows a pod from an unauthorized service attempting to call the orders service, but its connection is rejected by the orders sidecar proxy because it lacks a valid identity, thus enforcing a zero-trust policy.

Learning Steps (Manual/Programmatic)
Create a GKE Cluster: In the GCP Console, create a GKE cluster. During creation, navigate to the "Features" section and check the box to "Enable Anthos Service Mesh". This will install the control plane components on your cluster.

Deploy Sample Applications: Using kubectl and your local terminal (configured to connect to your new GKE cluster), create a new Kubernetes namespace for your microservices. Enable automatic sidecar injection for this namespace by applying a label: kubectl label namespace your-namespace istio-injection=enabled.

Deploy Microservices: Deploy two simple microservices into this namespace. For example, a frontend deployment and an orders deployment. Because of the label you applied in the previous step, Anthos will automatically add the Envoy sidecar proxy to each pod as it's created.

Observe Unsecured Traffic: Initially, the mesh defaults to PERMISSIVE mTLS mode. This means services can accept both encrypted (mTLS) and plaintext traffic. You can exec into the frontend pod and use curl to successfully call the orders service.

Enforce Strict mTLS: Create a YAML file for a PeerAuthentication policy. This policy will configure the mesh to only allow mTLS traffic.

YAML

apiVersion: "security.istio.io/v1beta1"
kind: "PeerAuthentication"
metadata:
  name: "default"
  namespace: "your-namespace"
spec:
  mtls:
    mode: STRICT
Apply this policy using kubectl apply -f your-policy-file.yaml.

Verify Secure Traffic: The communication between frontend and orders will still work, but it is now automatically encrypted and authenticated via mTLS. You can verify this using istioctl command-line tools.

Test the Security Policy: Deploy a third, "unauthorized" pod into the same namespace (e.g., a simple busybox container). exec into this new pod and try to curl the orders service. The request will now fail with a connection error. This is because the Envoy proxy protecting the orders service sees that the incoming traffic is not part of the mTLS tunnel and rejects it, successfully securing your service.

----------------------

Absolutely. Here are ten architecture scenarios designed for hands-on learning, covering common and important patterns across AWS, Azure, and GCP.

Scenario 34 (AWS): Protecting a Web Application with AWS WAF 🛡️
Title: Protecting a Web Application with AWS WAF

Description: Learn how to secure a public-facing web application against common web exploits and bots, such as SQL injection and cross-site scripting, by deploying AWS WAF on an Application Load Balancer.

User Prompt: "How can I protect my website from common hacking attempts?"

Services Involved:

AWS WAF (Web Application Firewall)

Application Load Balancer (ALB)

EC2 Auto Scaling Group

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows internet traffic first hitting AWS WAF. WAF is depicted as a firewall shield. It inspects the traffic based on a set of rules. Malicious traffic (e.g., SQL injection attempt) is shown being blocked by WAF. Legitimate traffic is passed through to an Application Load Balancer (ALB). The ALB then distributes this clean traffic to a fleet of EC2 instances in an Auto Scaling Group.

Learning Steps (Manual/Programmatic)
Prerequisites: Have an existing public-facing Application Load Balancer with EC2 instances serving a simple web page.

Create a Web ACL: In the AWS Console, navigate to WAF & Shield. Click "Create web ACL".

Describe Web ACL and associate to AWS resources: Give your Web ACL a name (e.g., WebApp-ACL) and select the region where your ALB resides. Click "Add AWS resources" and select the Application Load Balancer you want to protect.

Add Managed Rule Groups: This is the easiest way to get started. Click "Add rules" and choose "Add managed rule groups". AWS provides pre-configured rule sets. Add the AWSManagedRulesCommonRuleSet, which protects against a wide range of common vulnerabilities. Add another rule group like AWSManagedRulesSQLiRuleSet to specifically target SQL injection attacks.

Set Rule Actions: For each rule group, leave the action as "Block". This means any request that matches a rule in the set will be blocked and will not reach your application.

Set Default Action: For the "Default web ACL action for requests that don't match any rules," ensure it is set to "Allow". This means traffic is allowed by default unless it matches a block rule.

Review and Create: Review your configuration and create the Web ACL. It will be immediately associated with your ALB.

Test the WAF: Try to access your application normally through a web browser; it should work. Now, try to simulate a simple attack. In the URL, add a suspicious string like ?param=<script>alert('xss')</script>. AWS WAF will identify this as a cross-site scripting attempt, block the request, and return a 403 Forbidden error page. You can then view the blocked request in the WAF console's "Sampled requests" overview.

Scenario 35 (AWS): Database Caching with Amazon ElastiCache ⚡
Title: Database Caching with Amazon ElastiCache

Description: Improve your application's performance and reduce database load by implementing an in-memory caching layer using Amazon ElastiCache for Redis.

User Prompt: "My database is overloaded with simple, repetitive read queries. How can I speed things up?"

Services Involved:

Amazon ElastiCache (for Redis)

EC2 (for the application server)

Amazon RDS or DynamoDB (the database)

VPC & Security Groups

Level: Intermediate

Estimated Time: 40 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an EC2 Application Server receiving a request. The server's logic is shown as a flowchart. First, it checks the ElastiCache for Redis Cluster for the requested data.

If the data exists (a "cache hit"), ElastiCache returns the data directly to the application server, which responds to the user. This path is short and fast.

If the data does not exist (a "cache miss"), the application server then queries the primary RDS Database. The database returns the data. The application server then writes this data into the ElastiCache cluster for future requests before finally responding to the user. This path is longer.

Learning Steps (Manual/Programmatic)
Set up the Network: In the AWS Console, create a VPC. Inside the VPC, create a security group for your application servers (app-sg) and another for your ElastiCache cluster (cache-sg).

Configure Security Group: Edit the inbound rules for the cache-sg. Add a rule that allows inbound traffic on the Redis port (6379) only from the app-sg. This ensures only your application can access the cache.

Launch an ElastiCache Cluster: Navigate to ElastiCache. Click "Create" and choose Redis. Give the cluster a name. Place it within your VPC and associate it with the cache-sg security group. Use a small node type (e.g., cache.t2.micro) for testing.

Launch an EC2 Instance: Launch an EC2 instance to simulate your application server. Place it in the same VPC and associate it with the app-sg security group.

Modify Application Code: SSH into your EC2 instance. Install a Redis client for your programming language (e.g., redis-py for Python). Modify your application code to implement the caching logic:

When a request for data comes in, first try to fetch it from the ElastiCache cluster using the cluster's endpoint URL.

If the data is found, return it immediately.

If the data is not found, fetch it from your primary database.

Before returning the data, write it to the ElastiCache cluster with a Time-To-Live (TTL), for example, 300 seconds.

Test the Performance: Run your application. The first request for a piece of data will be slow. Subsequent requests for the same data (within the 5-minute TTL) will be significantly faster. You can verify this by adding timing logs to your application code.

Scenario 36 (AWS): Decoupling Microservices with Amazon EventBridge 🌉
Title: Decoupling Microservices with Amazon EventBridge

Description: Build a scalable, event-driven architecture where microservices communicate asynchronously by publishing events to a central event bus, reducing dependencies and improving resilience.

User Prompt: "When a new order is placed, I need to notify the shipping service and the invoicing service without the order service knowing about them."

Services Involved:

Amazon EventBridge

AWS Lambda

IAM

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows three microservices represented by Lambda functions: Order Service, Shipping Service, and Invoicing Service. In the center is a large bus icon labeled Amazon EventBridge.
The Order Service is shown publishing an "Order Created" event to the EventBridge bus. It does not send the event to the other services directly.
EventBridge has two Rules configured. The first rule matches on "Order Created" events and has a Target, which is the Shipping Service Lambda. The second rule also matches on "Order Created" events and targets the Invoicing Service Lambda. Arrows show EventBridge invoking both target services in parallel after receiving the single event.

Learning Steps (Manual/Programmatic)
Create Lambda Functions: In the Lambda Console, create three simple Lambda functions: order-service, shipping-service, and invoicing-service. For now, their code can just print the event they receive to logs.

Grant Permissions: Create an IAM role for the order-service that grants it events:PutEvents permission on the default EventBridge event bus.

Modify Order Service: In the order-service code, use the AWS SDK to put a custom event onto the EventBridge bus. The event should have a Source (e.g., com.my-app.orders), a DetailType (e.g., Order Created), and a Detail body (a JSON object with order information).

Create EventBridge Rules: Navigate to the Amazon EventBridge console. Go to "Rules" and click "Create rule".

Rule 1 (Shipping): Give it a name like RouteOrdersToShipping. For the "Event pattern," define a pattern that matches your custom event: { "source": ["com.my-app.orders"], "detail-type": ["Order Created"] }.

For the "Target," select "Lambda function" and choose your shipping-service function.

Rule 2 (Invoicing): Create another rule named RouteOrdersToInvoicing. Use the exact same event pattern, but for the target, select your invoicing-service function.

Test the System: Invoke the order-service Lambda function manually from the console.

Verify Results: Go to the CloudWatch Logs for both the shipping-service and the invoicing-service. You will see that both functions were invoked and have log entries showing the "Order Created" event data. This proves the order-service was successfully decoupled from its consumers.

Scenario 37 (AWS): Automated CI/CD with AWS CodePipeline 🔄
Title: Automated CI/CD with AWS CodePipeline

Description: Create a fully automated continuous integration and continuous deployment (CI/CD) pipeline that builds, tests, and deploys your application to EC2 instances whenever you commit a change to your code repository.

User Prompt: "I want to set up an automated deployment pipeline for my application on AWS."

Services Involved:

AWS CodePipeline

AWS CodeCommit (or GitHub)

AWS CodeBuild

AWS CodeDeploy

EC2

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a four-stage pipeline.

Source: A developer commits code to a CodeCommit repository. This action triggers the pipeline.

Build: CodePipeline orchestrates the flow. It pulls the source code and sends it to CodeBuild. CodeBuild runs build commands (defined in a buildspec.yml file), runs unit tests, and creates a build artifact (e.g., a zip file).

Deploy: CodePipeline takes the build artifact and sends it to CodeDeploy.

EC2: CodeDeploy manages the deployment to a fleet of EC2 instances. It follows instructions in an appspec.yml file to stop the old application, install the new version, and start it, performing a rolling update.

Learning Steps (Manual/Programmatic)
Set up EC2 and CodeDeploy Agent: Launch an EC2 instance. Attach an IAM role that gives it permissions to communicate with CodeDeploy and S3. Install the CodeDeploy agent on the instance.

Create a CodeCommit Repo: In the CodeCommit console, create a repository and push your application code to it. Include two special files: buildspec.yml (for CodeBuild commands) and appspec.yml (for CodeDeploy instructions).

Create a Build Project: In CodeBuild, create a new build project. Point it to your CodeCommit repository. For the build environment, choose an appropriate image (e.g., AWS-managed Linux image). Specify that it should use the buildspec.yml file from the source code.

Create a Deployment Application: In CodeDeploy, create a new application. Then, create a "deployment group" within it. Configure the deployment group to target your EC2 instances (e.g., by tag). Select a deployment strategy like "One at a time" or "Half at a time".

Create the Pipeline: Go to CodePipeline and create a new pipeline.

Source Stage: Select "AWS CodeCommit" and choose your repository and branch.

Build Stage: Select "AWS CodeBuild" and choose the build project you created.

Deploy Stage: Select "AWS CodeDeploy" and choose the application and deployment group you created.

Trigger and Observe: Save the pipeline. It will automatically run for the first time. You can watch as each stage turns green upon successful completion. To test the full CI/CD flow, make a small code change, commit it, and push it to your CodeCommit repository. This will automatically trigger a new pipeline execution, deploying your change to the EC2 instance within minutes.

Scenario 38 (Azure): User Identity Management with Azure AD B2C 👤
Title: User Identity Management with Azure AD B2C

Description: Provide a secure, customizable, white-label identity management solution for your consumer-facing application, allowing users to sign up and sign in with local accounts or social providers.

User Prompt: "I need a login page for my app where users can create their own username and password."

Services Involved:

Azure Active Directory B2C

App Registrations

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Low (first 50,000 users are free)

Architecture Diagram (PNG) Description
The diagram shows a Web Application. When a user clicks "Sign Up," the application redirects them to a customizable page hosted by Azure AD B2C. This page contains a sign-up form. The user fills it out and submits. AD B2C creates a new user account in the B2C Directory. For a returning user clicking "Sign In," they are also sent to the AD B2C hosted page to enter their credentials. After successful authentication, AD B2C issues an ID token back to the web application. The application validates this token to grant the user access.

Learning Steps (Manual/Programmatic)
Create an Azure AD B2C Tenant: In the Azure Portal, search for and create a new "Azure AD B2C" resource. This creates a separate directory to hold your consumer identities.

Register Your Application: Switch to your newly created B2C directory. Go to "App registrations" and register a new application. For the "Supported account types," choose the consumer option. Set the "Redirect URI" to where your application will be running (e.g., https://jwt.ms for easy testing).

Create User Flows: This is the core of B2C. Go to "User flows" and click "+ New user flow".

Create a "Sign up and sign in" flow. Give it a name. Under "Identity providers," select "Email signup."

Under "User attributes," choose the information you want to collect during sign-up (e.g., Display Name, City). Choose the claims you want to be returned in the token after login.

Test the User Flow: In the "User flows" list, click on the flow you just created. Click "Run user flow." This will open a new browser tab showing your customizable sign-up/sign-in page.

Test Sign-Up: Click "Sign up now," enter an email address and password, and fill out the requested attributes. Complete the sign-up process.

Test Sign-In: After sign-up, you'll be redirected to the test redirect URI (https://jwt.ms) with a valid ID token. The contents of the token, including the claims you selected, will be displayed. This simulates what your real application would receive. Run the user flow again and test the sign-in process with the credentials you just created.

Scenario 39 (Azure): Migrating an On-Premises Database with DMS 🚚
Title: Migrating an On-Premises Database with Azure Database Migration Service

Description: Perform a minimal-downtime migration of an on-premises SQL Server database to a fully managed Azure SQL Database using the Azure Database Migration Service (DMS).

User Prompt: "How can I move my company's SQL Server database to the cloud without taking it offline for a long time?"

Services Involved:

Azure Database Migration Service (DMS)

Azure SQL Database

Virtual Network (VNet)

Level: Advanced

Estimated Time: 90 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an On-Premises SQL Server database. A network connection (e.g., a Site-to-Site VPN) links the on-premises network to an Azure VNet. Inside Azure, a Database Migration Service (DMS) instance is deployed into a subnet within the VNet. DMS is shown connecting to the on-premises SQL Server as the Source. DMS is also connected to a managed Azure SQL Database as the Target. Arrows indicate the flow: DMS pulls a full backup from the source, restores it to the target, and then sets up continuous transaction log shipping to keep the target in sync with any ongoing changes at the source.

Learning Steps (Manual/Programmatic)
Assess the Source Database: Use the "Azure Data Studio" tool with the "Azure SQL Migration" extension to assess your on-premises database for any compatibility issues that might block a migration.

Provision the Target: In the Azure Portal, create a target Azure SQL Database. Configure it with a suitable performance tier.

Provision the DMS Instance: Search for and create an instance of the Azure Database Migration Service. Ensure you deploy it into a VNet that has connectivity back to your on-premises network.

Create a Migration Project: Inside the DMS instance, create a new migration project. Select the source type (SQL Server) and the target type (Azure SQL Database). Choose "Online data migration" for minimal downtime.

Specify Source Details: Enter the connection details for your on-premises SQL Server, including its IP address and credentials.

Specify Target Details: Enter the connection details for the target Azure SQL Database you created.

Map Databases: Select the source database you want to migrate and map it to the target database.

Start the Migration: Review the summary and start the migration. DMS will begin the process:

It takes a full backup of the source and restores it to Azure SQL.

It then continuously applies new transaction log backups to keep the target synchronized with the source.

Perform the Cutover: The DMS dashboard will show you when the initial restore is complete and the two databases are in sync. At a planned time, stop all traffic to your on-premises application, wait for the final logs to be applied in DMS, and then click "Complete cutover." This finalizes the migration. You can then re-point your application's connection string to the new Azure SQL Database.

Scenario 40 (Azure): Path-Based Routing with Application Gateway 🛣️
Title: Path-Based Routing with Application Gateway

Description: Configure an Application Gateway to route traffic to different backend pools of servers based on the URL path in the user's request.

User Prompt: "I want requests for /video to go to my video servers and requests for /images to go to my image servers, all using the same domain name."

Services Involved:

Azure Application Gateway

Virtual Machines (or App Services)

Virtual Network (VNet)

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows user requests hitting a single Application Gateway. Two types of requests are shown: my-site.com/images/* and my-site.com/video/*.
The Application Gateway has a Listener on port 80/443. This listener is associated with a Routing Rule. The rule is a "path-based" rule.

A path map for /images/* points to a Backend Pool containing the "Image Servers".

A path map for /video/* points to a different Backend Pool containing the "Video Servers".
Arrows show the gateway intelligently forwarding the traffic to the correct set of backend VMs based on the URL.

Learning Steps (Manual/Programmatic)
Set Up Backends: In the Azure Portal, create two sets of Virtual Machines. For example, two VMs for serving images and two for serving video. Install a basic web server on each and create a simple index.html file that identifies the server type (e.g., "Hello from Image Server 1").

Create the Application Gateway: Search for "Application Gateway" and create a new one.

Configure it within a VNet.

Create a public frontend IP address.

Configure Backend Pools: In the gateway configuration, go to the "Backends" tab. Create two backend pools:

image-pool: Add the private IP addresses of your image server VMs.

video-pool: Add the private IP addresses of your video server VMs.

Configure Routing Rules: Go to the "Configuration" tab. This is where you connect the frontend to the backends.

Create a Listener for HTTP traffic on port 80.

Add a Routing Rule. Select the rule type "Path-based".

In the "Paths" section, add two path-based targets:

Path /images/*, Target image-pool.

Path /video/*, Target video-pool.

Set a default target (e.g., the image pool) for any requests that don't match.

Create and Test: Create the Application Gateway. Once it's deployed, find its public IP address.

Navigate to http://<gateway-ip>/images/. You should see the response from one of your image servers.

Navigate to http://<gateway-ip>/video/. You should see the response from one of your video servers.

Scenario 41 (GCP): DDoS Protection with Cloud Armor 🛡️
Title: DDoS Protection with Cloud Armor

Description: Protect your application hosted behind a Google Cloud Load Balancer from Distributed Denial of Service (DDoS) attacks and other web-based threats using Cloud Armor security policies.

User Prompt: "How do I defend my application against large-scale DDoS attacks?"

Services Involved:

Cloud Armor

Cloud Load Balancing (External HTTP/S)

Managed Instance Group (MIG)

Level: Intermediate

Estimated Time: 30 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows malicious traffic (from a botnet) and legitimate user traffic both heading towards a Google Cloud HTTP/S Load Balancer. Attached to the load balancer's backend service is a Cloud Armor Security Policy. The policy is shown inspecting incoming traffic. It identifies and blocks the DDoS traffic based on pre-configured rules (e.g., rate limiting, geo-blocking). Only the legitimate user traffic is allowed to pass through the load balancer to the backend Managed Instance Group.

Learning Steps (Manual/Programmatic)
Prerequisites: Have a working External HTTP(S) Load Balancer that forwards traffic to a backend service (e.g., a Managed Instance Group).

Create a Security Policy: In the GCP Console, navigate to Network Security -> Cloud Armor. Click "Create policy".

Configure Policy: Give your policy a name (e.g., webapp-defense-policy). Set the policy type to "Backend security policy". Set the default rule to "Allow" traffic.

Add a Geo-blocking Rule: Click "Add Rule".

Condition: Use the expression editor to create a condition like origin.region_code == 'CN'.

Action: Deny.

Priority: Give it a priority number (e.g., 1000). Lower numbers are evaluated first.

This rule will block all traffic originating from China.

Add a Rate-Limiting Rule: Add another rule.

Action: throttle.

Rate: Set the rate to 100 and interval to 60 seconds (100 requests per minute).

Enforce on: IP. This will throttle requests on a per-IP basis.

Priority: 1100.

Apply Policy to Backend: Go to the "Targets" tab for your policy. Click "Add Target". Select "Backend service" and choose the backend service associated with your external load balancer.

Test and Monitor: The policy is now active. You can use the Cloud Armor dashboard to see traffic that is being allowed or denied by your rules. To test the geo-blocking, you would need to use a VPN or proxy from the blocked region.

Scenario 42 (GCP): Real-time Updates with Firestore Triggers 🔄
Title: Real-time Updates with Firestore Triggers

Description: Build an event-driven function that automatically triggers in response to data changes (create, update, delete) in a Firestore database collection.

User Prompt: "When a user's profile document is updated in my database, I need to run some code automatically."

Services Involved:

Cloud Firestore

Cloud Functions

Level: Beginner

Estimated Time: 20 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a user's application writing or updating a document in a Cloud Firestore collection called "users". This write operation is shown as an event. An arrow points from this event to a Cloud Function. The function is configured with a "Firestore Trigger" that is listening for changes on the "users" collection. When the trigger fires, the Cloud Function executes its code, which might perform an action like sending a welcome email, updating an aggregate count, or calling another service.

Learning Steps (Manual/Programmatic)
Create a Firestore Database: In the GCP Console, go to Firestore. Click "Create Database" and start in test mode, which allows open access for development.

Create a Cloud Function: Go to Cloud Functions and create a new function.

Configure the Trigger:

Set the "Trigger type" to "Cloud Firestore".

For the "Event type," select google.firestore.document.write. This will trigger on create, update, and delete events.

For the "Document Path," specify the collection and a wildcard for the document ID, for example: users/{userId}.

Write the Function Code: Use the inline editor. Select a runtime like Node.js. The function will receive two arguments: data (containing the new state of the document) and context (containing metadata like the userId).

JavaScript

/**
 * Triggered by a change to a Firestore document.
 *
 * @param {!Object} event The Cloud Functions event.
 * @param {!Object} context The event metadata.
 */
exports.firestoreTrigger = (event, context) => {
  const resource = context.resource;
  console.log('Function triggered by change to: ' + resource);

  const updatedValue = event.value;
  console.log('Document data:');
  console.log(JSON.stringify(updatedValue.fields));
};
Deploy the Function: Deploy the function.

Test the Trigger: Go back to the Firestore console. Manually add a new document to the users collection. Give it some fields and data.

Check the Logs: Navigate to the Cloud Functions console, click on your function, and go to the "Logs" tab. You will see a new log entry showing that your function was triggered successfully and it will print the contents of the document you just created. Try updating the document and see a new log entry appear.

Scenario 43 (GCP): Training a Custom ML Model with Vertex AI 🤖
Title: Training a Custom ML Model with Vertex AI

Description: Go through the end-to-end machine learning lifecycle by uploading a dataset, training a custom model using AutoML, and deploying it to an endpoint for predictions, all within the Vertex AI unified platform.

User Prompt: "I have a CSV file of customer data and I want to train a model to predict which ones are likely to churn."

Services Involved:

Vertex AI (Datasets, Training, Endpoints)

Cloud Storage

Level: Intermediate

Estimated Time: 90 minutes (most of it waiting for training)

Cost Level: High

Architecture Diagram (PNG) Description
The diagram shows a unified workflow within the Vertex AI Platform.

Dataset: A Data Scientist uploads a CSV file to Cloud Storage. This data is then used to create a Vertex AI Dataset.

Training: The user starts a new AutoML Training Job. The job uses the Dataset as input. Vertex AI is shown automatically testing different model architectures and hyperparameters.

Model Registry: After training, the best model is automatically registered in the Vertex AI Model Registry.

Endpoint: The user then deploys the registered model to a Vertex AI Endpoint. This creates a scalable, RESTful API for getting predictions.

Prediction: An application is shown sending new, unseen data to the endpoint and receiving a prediction in return.

Learning Steps (Manual/Programmatic)
Prepare and Upload Data: Create a CSV file with your data. One column should be the "target" you want to predict (e.g., churned_yes_no). Upload this CSV file to a Cloud Storage bucket.

Create a Dataset: In the Vertex AI console, go to "Datasets". Create a new dataset. Select "Tabular" as the data type and "Regression/Classification" as the objective. Point it to the CSV file in your Cloud Storage bucket. Vertex AI will import and analyze the data.

Train the Model: Once the dataset is created, click "Train new model".

Choose your dataset and specify the "Target column" (the one you want to predict).

Select "AutoML" as the training method.

Under "Budget," specify a training budget in node hours (e.g., 1 hour for a simple model).

Start the training. This process can take a long time, often an hour or more.

Evaluate the Model: When training is complete, Vertex AI will provide a detailed evaluation page. You can see metrics like AUC ROC, precision-recall curves, and a confusion matrix that show how well the model performed. You can also see which features were most important in making predictions.

Deploy the Model: If you are satisfied with the model's performance, go to the "Deploy & Test" tab. Click "Deploy to endpoint". Give the endpoint a name and Vertex AI will create a dedicated, scalable prediction server for you. This can take 10-15 minutes.

Get Predictions: Once the endpoint is active, you can use the UI to enter sample data and get a prediction. Vertex AI also provides sample code (e.g., Python, curl) that shows you how to call your new model's REST API endpoint from an application.


------------
Of course. Here are fifteen more detailed architecture scenarios designed for hands-on learning across AWS, Azure, and GCP.

Scenario 44 (AWS): Simplified PaaS Deployment with Elastic Beanstalk 🧑‍🌾
Title: Simplified PaaS Deployment with Elastic Beanstalk

Description: Learn how to deploy a web application without managing the underlying infrastructure by providing your code to Elastic Beanstalk, which automatically handles provisioning, load balancing, scaling, and monitoring.

User Prompt: "I want to deploy my Python web app on AWS, but I don't want to deal with setting up servers, load balancers, or auto-scaling myself."

Services Involved:

AWS Elastic Beanstalk

EC2 (managed by Elastic Beanstalk)

S3 (managed by Elastic Beanstalk)

Application Load Balancer (managed by Elastic Beanstalk)

CloudWatch (managed by Elastic Beanstalk)

Level: Beginner

Estimated Time: 25 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a developer uploading a ZIP file containing their application code to the Elastic Beanstalk service. Elastic Beanstalk is depicted as a management layer that automatically creates and configures other AWS services. Arrows point from Elastic Beanstalk to an Application Load Balancer, an Auto Scaling Group, and a fleet of EC2 instances. The service also configures CloudWatch Alarms for monitoring. The user simply interacts with a single Elastic Beanstalk "Environment" URL to access their fully provisioned, load-balanced, and auto-scaled application.

Learning Steps (Manual/Programmatic)
Prepare Your Application: Create a simple web application (e.g., using Python Flask or Node.js Express). Zip the application source code into a single file (e.g., app.zip).

Create an Elastic Beanstalk Application: In the AWS Console, navigate to Elastic Beanstalk. Click "Create application." Give your application a name.

Create an Environment: Within the application, create a new "Web server environment".

Configure the Environment:

Platform: Choose a managed platform that matches your application's language, like "Python" or "Node.js".

Source code: Select "Upload your code" and upload the app.zip file you created.

Presets: Choose a preset like "High availability" to automatically provision a load balancer and auto-scaling.

Review and Launch: Review the settings. Elastic Beanstalk will show you all the resources it's about to create. Click "Create environment."

Monitor Provisioning: The process will take 5-10 minutes. Elastic Beanstalk's dashboard will show you the status and events as it creates the S3 bucket, security groups, EC2 instances, and load balancer.

Access Your Application: Once the environment's health is "Ok," a URL will appear at the top of the dashboard. Click this URL to see your live application.

Explore Configuration: Navigate through the environment's "Configuration" tab. Here you can adjust settings like instance types, scaling triggers, and database connections, all without directly touching the underlying resources.

Scenario 45 (AWS): Visual Workflow Orchestration with Step Functions 🎼
Title: Visual Workflow Orchestration with Step Functions

Description: Build a complex, multi-step serverless workflow using a visual designer. This example orchestrates an image processing workflow that involves parallel processing and error handling.

User Prompt: "I need to build a reliable process: when an image is uploaded, validate it, then create a thumbnail and extract metadata at the same time, and finally, update a database."

Services Involved:

AWS Step Functions

AWS Lambda

S3

Level: Advanced

Estimated Time: 60 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows the Step Functions Workflow Studio. The workflow starts with a "Validate Image" Lambda function. An arrow points to a "Parallel" state. Inside the parallel state, two branches execute simultaneously: one branch calls a "Create Thumbnail" Lambda function, and the other calls an "Extract Metadata" Lambda function. After both parallel branches complete, a final arrow points to a "Save to Database" Lambda function. A separate "Catch" arrow points from the main flow to an "Error Handler" Lambda, showing the built-in error handling capability.

Learning Steps (Manual/Programmatic)
Create Lambda Functions: Create four simple Lambda functions: validate-image, create-thumbnail, extract-metadata, and save-to-database. For the demo, they can just contain log statements indicating their purpose.

Create a State Machine: In the Step Functions console, click "Create state machine." Choose to "Design your workflow visually."

Build the Workflow:

Drag a Lambda Invoke action onto the canvas and configure it to call your validate-image function.

Drag a "Parallel" state from the "Flow" menu and drop it after the first step.

Inside the first parallel branch, drag another Lambda Invoke action and configure it for the create-thumbnail function.

Inside the second parallel branch, do the same for the extract-metadata function.

After the Parallel state, drag a final Lambda Invoke action for the save-to-database function.

Generate IAM Role: Step Functions can automatically generate an IAM role with the necessary permissions to invoke the Lambda functions you've included in your workflow.

Review and Create: Review the generated Amazon States Language (ASL) definition of your workflow and create the state machine.

Start an Execution: On your state machine's page, click "Start execution." You can provide a sample JSON input if your functions expect it.

Visualize the Execution: The console will show a live, color-coded graph of your workflow execution. You can see each step turn green as it completes successfully. You can click on any step to see its input and output, making it easy to debug complex flows.

Scenario 46 (AWS): Securely Managing Application Secrets with Secrets Manager 🤫
Title: Securely Managing Application Secrets with Secrets Manager

Description: Learn how to stop hardcoding secrets like database passwords in your application code. Store them securely in AWS Secrets Manager and retrieve them at runtime using IAM permissions.

User Prompt: "Where should I store the database password for my application instead of putting it in my source code?"

Services Involved:

AWS Secrets Manager

IAM

EC2 or AWS Lambda (as the application)

Level: Intermediate

Estimated Time: 20 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows an EC2 Instance or Lambda Function running application code. The code needs a database password. An arrow points from the application to the AWS Secrets Manager service, labeled "API Call: GetSecretValue". An IAM Role is attached to the EC2/Lambda, which grants it permission to access a specific secret. Secrets Manager is shown returning the secret value securely to the application at runtime. The diagram also shows Secrets Manager's ability to automatically rotate secrets by communicating with a service like Amazon RDS.

Learning Steps (Manual/Programmatic)
Store a New Secret: In the AWS Console, navigate to Secrets Manager. Click "Store a new secret."

Configure the Secret:

Choose "Other type of secret."

Create key-value pairs for your secret, for example: username with value admin and password with value MySuperSecretPassword123.

Choose an encryption key (the default AWS-managed key is fine).

Give your secret a name, like prod/MyWebApp/DatabaseCredentials.

Configure Rotation (Optional): On the next screen, you can configure automatic secret rotation for supported services like RDS. For this demo, you can disable it.

Create an IAM Policy: Go to IAM and create a new policy. Use the visual editor. For the service, select Secrets Manager. For actions, choose GetSecretValue. For resources, specify the ARN of the secret you just created. This ensures the policy only grants access to this one secret.

Attach Policy to Role: Attach this new policy to the IAM role used by your EC2 instance or Lambda function.

Update Application Code: Modify your application code to use the AWS SDK. Instead of having a hardcoded password, the code should make an API call to Secrets Manager at startup to fetch the secret value using its name or ARN.

Test the Retrieval: Deploy and run your application. Check the logs to confirm that it successfully fetched the secret from Secrets Manager and was able to use it to connect to the database.

Scenario 47 (AWS): Real-time Stream Analytics with Kinesis Data Analytics 🌊
Title: Real-time Stream Analytics with Kinesis Data Analytics

Description: Learn to run continuous SQL queries against a real-time data stream to perform time-series analysis, detect anomalies, and generate alerts without managing any servers.

User Prompt: "I need to analyze sensor data in real-time and alert me if the temperature goes over a certain average for 1 minute."

Services Involved:

Amazon Kinesis Data Streams (for ingestion)

Amazon Kinesis Data Analytics for SQL

AWS Lambda (as a destination for alerts)

Level: Advanced

Estimated Time: 45 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows sensor data flowing into a Kinesis Data Stream. This stream is configured as the source for a Kinesis Data Analytics for SQL Application. The application contains a continuous SQL query with a "tumbling window" function that calculates the average temperature every minute. The query's output is sent to a second stream, called an "alert stream." A Lambda function is subscribed to this alert stream. When the average temperature in the query results exceeds a threshold, the Lambda function is invoked and can perform an action, like sending an SNS notification.

Learning Steps (Manual/Programmatic)
Create a Kinesis Data Stream: In the Kinesis console, create a data stream to act as the source for your raw data.

Create a Kinesis Data Analytics Application: Navigate to Kinesis Data Analytics. Create a new application, choosing the "SQL" runtime.

Connect to Source: Connect your application to the Kinesis data stream you created in step 1. Kinesis will attempt to discover the schema of the incoming data. You may need to edit it to match your data structure (e.g., define temperature as a DOUBLE).

Write Real-time SQL: Go to the "SQL editor." Write a SQL query to perform a time-series aggregation. For example:

SQL

CREATE OR REPLACE STREAM "DESTINATION_SQL_STREAM" (
    average_temp DOUBLE,
    max_temp DOUBLE
);

CREATE OR REPLACE PUMP "STREAM_PUMP" AS
    INSERT INTO "DESTINATION_SQL_STREAM"
    SELECT STREAM AVG(temperature), MAX(temperature)
    FROM "SOURCE_SQL_STREAM_001"
    GROUP BY STEP("SOURCE_SQL_STREAM_001".ROWTIME BY INTERVAL '1' MINUTE);
Connect to Destination: Configure a destination for your query results. You can send it to another Kinesis stream or directly to a Lambda function. Choose Lambda and select a function that will handle the alerts.

Run the Application: Start the application.

Send Data: Use a simple script (e.g., Python with boto3) or the AWS CLI to send sample sensor data records (JSON format) to the source Kinesis stream.

Verify Results: Check the CloudWatch logs for your destination Lambda function. You will see it being invoked every minute with the aggregated results calculated by your continuous SQL query.

Scenario 48 (Azure): Deploying Kubernetes with Azure Kubernetes Service (AKS) ⚓
Title: Deploying Kubernetes with Azure Kubernetes Service (AKS)

Description: Learn how to create, manage, and deploy containerized applications to a production-ready managed Kubernetes cluster on Azure.

User Prompt: "I need to set up a managed Kubernetes cluster on Azure to run my microservices."

Services Involved:

Azure Kubernetes Service (AKS)

Azure Container Registry (ACR)

Virtual Network (VNet)

Level: Intermediate

Estimated Time: 45 minutes

Cost Level: High

Architecture Diagram (PNG) Description
The diagram shows a developer using kubectl on their local machine. They are applying a deployment.yaml file. The kubectl commands are sent to the AKS Control Plane, which is fully managed by Azure. The control plane then schedules Pods to run on a set of Worker Nodes (which are Azure VMs) in a node pool. The deployment.yaml specifies a container image, which the worker nodes pull from a private Azure Container Registry (ACR). A Load Balancer service is also shown, exposing the application running on the pods to the internet.

Learning Steps (Manual/Programmatic)
Prerequisites: Install the Azure CLI (az) and kubectl on your local machine.

Create a Resource Group: In the Azure Portal or using the CLI, create a resource group.

Create an Azure Container Registry (ACR): Create an ACR instance to store your Docker images.

Create an AKS Cluster: Run the az aks create command in your terminal. Specify the resource group, a cluster name, and enable the addon to attach your ACR instance. This command automates the creation of the control plane, worker nodes, and networking.

Connect kubectl to AKS: Run az aks get-credentials. This command configures your local kubectl to securely connect to your new AKS cluster.

Build and Push a Container Image: Take a sample application with a Dockerfile. Use Docker to build an image, tag it with your ACR's login server name, and push it to your ACR.

Create a Kubernetes Deployment Manifest: Create a deployment.yaml file. In the spec, define the container image to be your newly pushed image from ACR. Also, create a service.yaml file with a type: LoadBalancer to expose your deployment to the internet.

Deploy the Application: Use kubectl apply -f deployment.yaml and kubectl apply -f service.yaml to deploy your application.

Verify Deployment: Run kubectl get pods to see your application pods running. Run kubectl get service to find the external IP address of your load balancer. Access this IP in a browser to see your live application.

Scenario 49 (Azure): Managing Secrets with Azure Key Vault 🔑
Title: Managing Secrets with Azure Key Vault

Description: Securely store and control access to tokens, passwords, certificates, and API keys used by your cloud applications using Azure Key Vault.

User Prompt: "How do I securely give my Azure Web App access to a database connection string?"

Services Involved:

Azure Key Vault

Azure App Service

Managed Identity

Level: Intermediate

Estimated Time: 25 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows an Azure App Service instance. A "Managed Identity" is enabled for this App Service, represented by a badge icon. An Azure Key Vault is shown holding secrets. The Key Vault has an "Access Policy" that explicitly grants Get permission to the App Service's managed identity. An arrow shows the App Service making a secure, authenticated call to the Key Vault at runtime to retrieve a secret (like a connection string). The application code never contains the secret itself.

Learning Steps (Manual/Programmatic)
Create an Azure Key Vault: In the Azure Portal, search for and create a Key Vault.

Create an App Service: Create a new Azure App Service to host your web application.

Enable Managed Identity: In the settings for your App Service, go to the "Identity" blade. Switch the "System assigned" status to "On". This creates a service principal in Azure AD that represents your application.

Add a Secret to Key Vault: Go to your Key Vault. In the "Secrets" blade, click "Generate/Import" to add a new secret. Give it a name (e.g., DatabaseConnectionString) and enter the secret value.

Create an Access Policy: In the Key Vault's "Access policies" blade, click "+ Create".

Under "Secret permissions," grant the Get and List permissions.

In the "Principal" selection, search for and select the name of your App Service (the managed identity you enabled in step 3).

Create the policy.

Reference the Secret in App Service: Go back to your App Service. In the "Configuration" blade, add a new "Application setting". The name can be anything (e.g., DB_CONNECTION). For the value, use a special Key Vault reference syntax: @Microsoft.KeyVault(SecretUri=https://<your-key-vault-name>.vault.azure.net/secrets/<your-secret-name>/).

Test the Application: Your application code can now read the secret simply by reading the DB_CONNECTION environment variable. The App Service platform handles the authentication to Key Vault and the injection of the secret value. Deploy code that prints this environment variable to verify it's working.

Scenario 50 (Azure): Modern Data Warehousing with Azure Synapse Analytics 💎
Title: Modern Data Warehousing with Azure Synapse Analytics

Description: Use a unified analytics platform to ingest data from multiple sources, transform it using serverless Spark pools, and serve it for BI and reporting using a dedicated SQL pool.

User Prompt: "I need a single platform to handle my data engineering, data warehousing, and BI needs."

Services Involved:

Azure Synapse Analytics

Synapse Pipelines (similar to Data Factory)

Apache Spark Pools (for data transformation)

Dedicated SQL Pools (for data warehousing)

Azure Data Lake Storage (ADLS) Gen2

Level: Advanced

Estimated Time: 75 minutes

Cost Level: High

Architecture Diagram (PNG) Description
The diagram shows the Azure Synapse Analytics Workspace as a unified environment.

Ingest: A Synapse Pipeline pulls raw data from external sources and lands it in Azure Data Lake Storage (ADLS) Gen2.

Prepare & Train: A Data Engineer uses a notebook in a serverless Apache Spark Pool to read the raw data from ADLS, clean it, transform it, and write the curated data back to a different folder in ADLS.

Serve: The pipeline then loads the curated data from ADLS into a high-performance Dedicated SQL Pool (data warehouse).

Visualize: A Business Analyst connects Power BI to the Dedicated SQL Pool to create reports and dashboards.

Learning Steps (Manual/Programmatic)
Create a Synapse Workspace: In the Azure Portal, create an Azure Synapse Analytics workspace. This will also require you to create or link an ADLS Gen2 storage account.

Launch Synapse Studio: Open the Synapse Studio web interface from your workspace's overview page.

Upload Sample Data: In the "Data" hub, navigate to your linked ADLS account and upload a sample CSV file with some raw data.

Create a Spark Pool: In the "Manage" hub, create a new serverless Apache Spark pool. Choose a small node size.

Transform Data with a Notebook: In the "Develop" hub, create a new Spark notebook. Write PySpark code to read your raw CSV from the data lake into a DataFrame, perform some transformations (e.g., filter rows, change data types), and write the result back to the data lake in a more efficient format like Parquet.

Create a Dedicated SQL Pool: In the "Manage" hub, create a new Dedicated SQL pool. This is your provisioned data warehouse. Start with the smallest size (DW100c).

Load Data into SQL Pool: Use a Synapse Pipeline or a Spark notebook to load the curated Parquet data from your data lake into a table in your Dedicated SQL Pool. The COPY command is highly efficient for this.

Query the Data: In the "Develop" hub, create a new SQL script. Connect to your Dedicated SQL Pool and run standard T-SQL queries against your newly loaded table to perform analysis.

Scenario 51 (Azure): Securing VNet Subnets with Network Security Groups (NSGs) 🚦
Title: Securing VNet Subnets with Network Security Groups (NSGs)

Description: Learn the fundamentals of network security in Azure by creating and applying Network Security Groups (NSGs) to control inbound and outbound traffic for subnets and virtual machines.

User Prompt: "I want to create a 'DMZ' subnet that can be reached from the internet and a 'private' subnet that cannot, and only allow the DMZ to talk to the private subnet."

Services Involved:

Virtual Network (VNet)

Network Security Groups (NSGs)

Virtual Machines

Level: Beginner

Estimated Time: 30 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a VNet with two subnets: DMZ Subnet and Private Subnet. A Web Server VM is in the DMZ Subnet, and a Database Server VM is in the Private Subnet.

An NSG attached to the DMZ Subnet has an inbound rule allowing TCP traffic on port 443 from Any source.

An NSG attached to the Private Subnet has an inbound rule allowing TCP traffic on port 1433 (SQL) only from the IP address range of the DMZ Subnet. It has another rule denying all other inbound traffic from Any source.
This enforces the rule that the public can only reach the web server, and only the web server can reach the database server.

Learning Steps (Manual/Programmatic)
Create a VNet with Two Subnets: In the Azure Portal, create a VNet. Within it, create two subnets: DMZ-Subnet and Private-Subnet.

Create Two NSGs: Search for "Network security groups" and create two NSGs: DMZ-NSG and Private-NSG.

Configure DMZ-NSG:

Go to the DMZ-NSG and select "Inbound security rules."

Add a new rule: Priority 100, Name Allow-HTTPS, Port 443, Protocol TCP, Source Any, Action Allow.

Configure Private-NSG:

Go to the Private-NSG and select "Inbound security rules."

Add a new rule: Priority 100, Name Allow-SQL-From-DMZ, Port 1433, Protocol TCP, Source Address Prefix, Source address prefix (enter the CIDR of DMZ-Subnet, e.g., ********/24), Action Allow.

Associate NSGs with Subnets: Go to your VNet's "Subnets" blade.

Click on DMZ-Subnet and in the "Network security group" dropdown, select DMZ-NSG.

Click on Private-Subnet and select Private-NSG.

Deploy VMs: Create two VMs. Place the "Web-VM" in the DMZ-Subnet and the "DB-VM" in the Private-Subnet.

Test Connectivity: The Web-VM will have a public IP and you can attempt to access it on port 443. The DB-VM will have no public IP. If you RDP into the Web-VM, you should be able to connect to the DB-VM on port 1433. An attempt to connect directly to the DB-VM from the internet will fail.

Scenario 52 (GCP): Caching Content with Cloud CDN 💨
Title: Caching Content with Cloud CDN

Description: Learn how to accelerate your website's performance by caching static content (like images, CSS, and JS files) at Google's globally distributed edge locations.

User Prompt: "My website is slow for users who are far away from my server. How can I make it faster for everyone?"

Services Involved:

Cloud CDN

Cloud Storage

External HTTP(S) Load Balancer

Level: Beginner

Estimated Time: 25 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a central Cloud Storage bucket acting as the "Origin Server," holding static website files. An External HTTP(S) Load Balancer is configured to use this bucket as its backend. The Cloud CDN feature is enabled on this backend. A user in a distant location (e.g., Australia) makes a request. The request is routed to the nearest Google Edge Cache. If the content isn't there (cache miss), the edge cache fetches it from the origin Cloud Storage bucket in the US, serves it to the user, and stores a copy. The next user in Australia who requests the same file gets it directly from the local edge cache, resulting in a much faster load time.

Learning Steps (Manual/Programmatic)
Create and Populate a Cloud Storage Bucket: In the GCP Console, create a new Cloud Storage bucket. Upload some static files like index.html, style.css, and a large image file.

Make Bucket Public: To allow the load balancer to access the files, make the bucket publicly readable by adding the allUsers principal with the Storage Object Viewer role under the bucket's permissions.

Create a Load Balancer: Go to Network Services -> Load balancing. Create an "HTTP(S) Load Balancer".

Configure Backend: In the "Backend configuration" step, select "Create a backend bucket." Choose the Cloud Storage bucket you created. Crucially, check the box that says "Enable Cloud CDN". You can leave the cache settings as default.

Configure Frontend: In the "Frontend configuration" step, a public IP address and port will be created for you.

Review and Create: Finalize the creation of the load balancer.

Test the CDN: Find the public IP address of your load balancer. Open your browser's developer tools and go to the "Network" tab. Access http://<load-balancer-ip>/your-image.jpg.

Verify Caching: Observe the response headers for the image. You should see headers like Age and x-cache: HIT, indicating the content was served from the CDN cache. The first request might be a MISS, but subsequent requests should be a HIT.

Scenario 53 (GCP): Serverless Web Hosting with App Engine ⚙️
Title: Serverless Web Hosting with App Engine

Description: Deploy a web application using Google's original Platform-as-a-Service (PaaS), App Engine, which manages scaling, versioning, and traffic splitting for you.

User Prompt: "I need a simple way to deploy my Python application and have it scale automatically without thinking about servers."

Services Involved:

App Engine

Cloud Build (used implicitly)

Level: Beginner

Estimated Time: 20 minutes

Cost Level: Low (generous free tier)

Architecture Diagram (PNG) Description
The diagram shows a developer with application code and an app.yaml configuration file. They use the gcloud CLI to run the gcloud app deploy command. This command uploads the code to Cloud Build, which builds a container image and stores it. App Engine then creates a new "Version" of the service using this image. App Engine automatically manages a fleet of instances, scaling them from zero to many based on incoming traffic. User traffic hits a central App Engine URL, which routes it to the currently active instances.

Learning Steps (Manual/Programmatic)
Prerequisites: Install the gcloud command-line tool.

Create a Sample App: Create a simple web application in a supported language like Python, Go, or Node.js.

Create app.yaml: In the root of your project, create a file named app.yaml. This file tells App Engine how to run your application.

YAML

runtime: python39 # Specify your language and version
entrypoint: gunicorn -b :$PORT main:app # Specify how to start your app
Create an App Engine Application: In your terminal, run gcloud app create and choose a region for your application. This is a one-time setup step for a project.

Deploy the Application: In the root directory of your project, run the command gcloud app deploy.

Confirm Deployment: The CLI will show you the details of the service to be deployed. Type Y to continue. gcloud will zip your code, upload it, invoke Cloud Build to create a runnable image, and deploy it as a new version on App Engine.

View Your Application: After a few minutes, the command will finish and provide you with a URL. You can also run gcloud app browse to open your live application in a web browser.

Manage Versions: Deploy a small change to your code using gcloud app deploy again. Go to the App Engine -> Versions page in the GCP console. You will see both the old and new versions listed. You can easily split traffic between them or roll back to the previous version with a single click.

Scenario 54 (GCP): Zero-Trust Application Access with Identity-Aware Proxy (IAP) 🏰
Title: Zero-Trust Application Access with Identity-Aware Proxy (IAP)

Description: Secure an application running on Compute Engine or App Engine by enforcing user identity-based access controls at the edge, instead of relying on network-level firewalls or VPNs.

User Prompt: "I want only employees in my company to be able to access our internal admin app, without setting up a VPN."

Services Involved:

Identity-Aware Proxy (IAP)

Cloud Load Balancing

Compute Engine or App Engine

IAM

Level: Intermediate

Estimated Time: 40 minutes

Cost Level: Low (IAP itself is free, you pay for load balancer/compute)

Architecture Diagram (PNG) Description
The diagram shows two users trying to access an application hosted on a Managed Instance Group behind a Load Balancer.

User 1 (Authorized): This user is part of the company's Google Workspace. Their request hits the Identity-Aware Proxy (IAP), which is enabled on the load balancer. IAP redirects the user to the Google sign-in page. The user authenticates. IAP checks their identity against an IAM Policy that grants them the "IAP-secured Web App User" role. The check passes, and the user's request is forwarded to the application.

User 2 (Unauthorized): This user is external. Their request also hits IAP. They might be able to sign in with a personal Google account, but when IAP checks their identity against the IAM policy, it finds no match. IAP blocks the request and shows the user an access denied page.

Learning Steps (Manual/Programmatic)
Prerequisites: Have an application running on a backend service (like a Managed Instance Group) behind an External HTTP(S) Load Balancer.

Configure OAuth Consent Screen: Go to APIs & Services -> OAuth consent screen. Configure it as "Internal" so only users in your organization can use it. Provide an application name and support email.

Enable IAP: Go to Security -> Identity-Aware Proxy. You will see a list of your backend resources. Find the backend service for your load balancer and toggle the switch in the "IAP" column to On.

Grant Access: A side panel will appear. Click "Add Principal." Enter the email address of a user, group (e.g., <EMAIL>), or service account. Grant them the role "IAP-secured Web App User".

Test Access (as Authorized User): In an incognito browser window, navigate to the public IP address of your load balancer. You will be redirected to the Google sign-in page. Log in with the user account you granted access to in the previous step. You should be able to see your application.

Test Access (as Unauthorized User): Open another incognito browser window or use a different browser profile. Navigate to the same IP address. Log in with a personal Gmail account or any other account not on the allow list. You will be presented with a "You don't have access" page, proving that IAP has successfully blocked the unauthorized user before they could reach your application.

Scenario 55 (GCP): Storing and Querying JSON with Firestore 📄
Title: Storing and Querying JSON with Firestore

Description: Learn how to use Cloud Firestore, a flexible, scalable NoSQL document database, to store and query complex, nested JSON-like documents for your web and mobile applications.

User Prompt: "I need a simple NoSQL database to store user profiles that have nested data and arrays."

Services Involved:

Cloud Firestore

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low (generous free tier)

Architecture Diagram (PNG) Description
The diagram shows the hierarchical structure of Firestore. A top-level box is labeled Database. Inside, there are several Collections (e.g., users, products). Within the users collection, there are multiple Documents, each with a unique ID (e.g., user_123). Each document contains key-value pairs, where values can be simple types (strings, numbers), nested objects (Maps), or lists (Arrays), representing a rich JSON structure. A mobile/web app is shown directly querying the users collection to fetch a specific document.

Learning Steps (Manual/Programmatic)
Create a Firestore Database: In the GCP Console, navigate to Firestore. Click "Create Database". Choose "Native mode" and select a location. For the security rules, start in "Test mode" to allow open access for development.

Start a Collection: In the Firestore data viewer, click "Start collection." Enter a Collection ID, for example, users.

Create Your First Document: Click "Auto-ID" to generate a unique document ID.

Add a field named firstName of type string with a value like "Ada".

Add a field named lastName of type string with a value like "Lovelace".

Add a field named birthYear of type number with a value of 1815.

Add Nested Data:

Click the "+" to add another field. Name it address. For the type, select map. This creates a nested object. Inside this map, add fields like street and city.

Add another field named interests. For the type, select array. Inside this array, add several string values like "mathematics", "programming", and "music".

Save the Document: Save the document. You have now created a complex, nested document.

Query Your Data: At the top of the collection view, you can build queries.

Click "Add to query."

Build a simple query where birthYear is == 1815. The UI will show the document you created.

Try a more complex query where interests array-contains programming. This will also find your document. This demonstrates Firestore's powerful querying capabilities on complex data structures.


--------------------










Of course. Here are ten basic, foundational scenarios focused on the most common "first steps" a learner would take in each major cloud.

Scenario 56 (AWS): Launching a Simple EC2 Web Server 🖥️
Title: Launching a Simple EC2 Web Server

Description: Learn the most fundamental Infrastructure as a Service (IaaS) concept by launching a single Linux virtual machine, installing a basic web server on it, and making it accessible from the internet.

User Prompt: "I need a simple virtual server in the cloud to host a test website."

Services Involved:

EC2 (Elastic Compute Cloud)

VPC (Virtual Private Cloud)

Security Groups

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low (Free Tier eligible)

Architecture Diagram (PNG) Description
The diagram shows an AWS VPC with a public subnet connected to an Internet Gateway. Inside the public subnet is a single EC2 Instance. A Security Group is attached to the EC2 instance, acting as a firewall. The security group has an inbound rule allowing traffic on port 80 (HTTP) from any IP address (0.0.0.0/0). A user is shown connecting via the internet, through the gateway, to the EC2 instance to view a web page.

Learning Steps (Manual/Programmatic)
Navigate to EC2: In the AWS Management Console, go to the EC2 service.

Launch Instance: Click the "Launch instances" button.

Choose an AMI: Give your server a name. For the "Amazon Machine Image (AMI)," select "Amazon Linux 2," which is a common, free-tier eligible choice.

Choose an Instance Type: Select the t2.micro instance type, which is also eligible for the free tier.

Create a Key Pair: For a real server, you'd create a key pair to log in via SSH. For this simple demo, you can proceed without one.

Configure Network Settings: In the "Network settings" panel, click "Edit."

Ensure a VPC and a public subnet are selected.

Under "Firewall (security groups)," select "Create security group."

Add a security group rule: Check the box for "Allow HTTP traffic from the internet." This automatically creates a rule allowing inbound traffic on port 80 from anywhere.

Configure User Data: Expand the "Advanced details" section. In the "User data" field, paste a simple script that will run when the instance first boots. This script will install and start a web server.

Bash

#!/bin/bash
yum update -y
yum install -y httpd
systemctl start httpd
systemctl enable httpd
echo "<h1>Hello from my EC2 Web Server!</h1>" > /var/www/html/index.html
Launch and Verify: Review the summary and click "Launch instance." After a few minutes, the instance state will change to "Running." Select the instance and find its "Public IPv4 address" in the details pane. Paste this IP address into your web browser. You should see the "Hello from my EC2 Web Server!" message.

Scenario 57 (AWS): Storing Files in S3 🗃️
Title: Storing Files in Amazon S3

Description: Learn the basics of object storage by creating an S3 bucket and uploading files to it. Understand the difference between private and public objects.

User Prompt: "I need a place to store my application's user-uploaded images."

Services Involved:

S3 (Simple Storage Service)

Level: Beginner

Estimated Time: 10 minutes

Cost Level: Low (Free Tier eligible)

Architecture Diagram (PNG) Description
The diagram is very simple. It shows a user on their computer and an icon for the Amazon S3 service. An arrow points from the user to S3, labeled "Upload File." Inside the S3 icon is a "Bucket" which in turn contains several "Objects" (files). A second arrow points from an object back to the user, labeled "Download File," to show that data can be retrieved.

Learning Steps (Manual/Programmatic)
Navigate to S3: In the AWS Console, go to the S3 service.

Create a Bucket: Click "Create bucket."

Bucket names must be globally unique. Enter a unique name, like yourname-my-first-app-files-123.

Select an AWS Region.

Leave the "Block all public access" setting checked. This is the default and most secure option.

Confirm Creation: Review the settings and click "Create bucket."

Upload a File: Click on your newly created bucket's name to enter it. Click the "Upload" button. Drag and drop a file from your computer or click "Add files." Click "Upload" at the bottom of the page.

Observe Private Access: Once uploaded, click on the file's name. Try to open the "Object URL." You will get an "Access Denied" XML error. This is because the bucket and the object are private by default.

Make an Object Public (for testing): Go back to the object's details. Select the object, click the "Actions" menu, and choose "Make public using ACL." Acknowledge the warning and confirm.

Verify Public Access: Now, try opening the "Object URL" again. Your file should now display correctly in the browser, demonstrating you've successfully changed its permissions. In a real application, you would generate temporary, secure "presigned URLs" instead of making objects permanently public.

Scenario 58 (AWS): Sending Notifications with SNS 📢
Title: Sending Notifications with Amazon SNS

Description: Learn how to use a publish/subscribe (pub/sub) messaging pattern to send a single message to multiple subscribers, such as email addresses or SMS endpoints.

User Prompt: "I need to send an email alert to my entire dev team when a critical error occurs."

Services Involved:

Amazon SNS (Simple Notification Service)

Level: Beginner

Estimated Time: 10 minutes

Cost Level: Low (Free Tier eligible)

Architecture Diagram (PNG) Description
The diagram shows a central SNS Topic icon. An "Application" or "Service" is shown publishing a single "Message" to this topic. The topic has multiple Subscriptions. Arrows point from the topic to each subscriber. The subscribers are shown as different icons: an email icon, an SMS (phone) icon, and a Lambda function icon, demonstrating that one message can be fanned out to multiple different types of endpoints.

Learning Steps (Manual/Programmatic)
Navigate to SNS: In the AWS Console, go to the Simple Notification Service (SNS).

Create a Topic: In the left navigation, click "Topics," then "Create topic."

Choose the "Standard" type.

Give the topic a name, for example, Critical-Alerts.

Create Subscriptions: Click on your newly created topic. In the "Subscriptions" tab, click "Create subscription."

Subscriber 1 (Email): For the "Protocol," select Email. For the "Endpoint," enter your email address. Click "Create subscription."

Subscriber 2 (Another Email): Repeat the process to add a second email address.

Confirm Subscriptions: Check your email inbox for each address you subscribed. You will receive an email from AWS with a confirmation link. You must click this link to activate the subscription. The status in the console will change from "Pending confirmation" to "Confirmed."

Publish a Message: Go back to your topic's page and click the "Publish message" button.

Enter a "Subject" (e.g., "Critical Server Failure").

Enter a "Message body" (e.g., "The main production server is unresponsive. Please investigate immediately.").

Verify Delivery: Click "Publish message." Within moments, check the inboxes for both email addresses you subscribed. Both should have received the exact same alert email, demonstrating the fan-out pattern.

Scenario 59 (AWS): Creating a Managed Relational Database with RDS 🐘
Title: Creating a Managed Relational Database with RDS

Description: Learn how to launch a fully managed relational database, such as PostgreSQL or MySQL, using Amazon RDS. This handles patching, backups, and failovers for you.

User Prompt: "I need a standard PostgreSQL database for my application, but I don't want to manage the server myself."

Services Involved:

Amazon RDS (Relational Database Service)

VPC & Security Groups

Level: Beginner

Estimated Time: 20 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an Amazon RDS DB Instance running inside a private subnet of a VPC. A Security Group is attached to the RDS instance, with an inbound rule allowing traffic on the database port (e.g., 5432 for PostgreSQL). An Application Server (EC2 instance) is shown in a separate subnet. An arrow points from the application server to the RDS instance, indicating the database connection. The diagram notes that RDS manages backups, patching, and high availability for the user.

Learning Steps (Manual/Programmatic)
Navigate to RDS: In the AWS Console, go to the RDS service.

Create Database: On the dashboard, click "Create database."

Choose Creation Method: Select "Standard Create."

Choose Engine: Select a database engine, for example, "PostgreSQL."

Choose Template: Select the "Free tier" template to minimize costs for this demo.

Configure Settings:

DB instance identifier: Give your database a name (e.g., my-app-db).

Master username: Set a username (e.g., postgres).

Master password: Set a secure password.

Configure Connectivity:

Choose a VPC to launch the database into.

Under "Public access," select "No". This is a critical security best practice. It means the database can only be reached from within the VPC.

For the "VPC security group," you can choose an existing one or create a new one.

Create the Database: Review the settings and click "Create database." Provisioning will take 10-15 minutes.

Configure Security Group: While it's creating, find the security group associated with the RDS instance. Edit its inbound rules to allow traffic on port 5432 from the security group of your application server. This allows your app to connect.

Connect to the Database: Once the database status is "Available," you can find its Endpoint name in the "Connectivity & security" tab. Use this endpoint, along with the username and password you created, in your application's connection string to connect to your new managed database.

Scenario 60 (Azure): Creating a Windows Virtual Machine 🖼️
Title: Creating a Windows Virtual Machine

Description: Learn how to launch a Windows Server virtual machine in Azure and connect to it using Remote Desktop Protocol (RDP).

User Prompt: "I need a Windows Server in the cloud to run some of my company's specific software."

Services Involved:

Azure Virtual Machines

Virtual Network (VNet)

Network Security Group (NSG)

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows an Azure VNet with a single Windows Server VM inside. A Network Security Group (NSG) is attached to the VM's network interface. The NSG has an inbound security rule allowing traffic on TCP port 3389 (RDP) from a specific IP address (the user's home/office IP). An arrow shows the user connecting from their computer to the VM's public IP address using RDP.

Learning Steps (Manual/Programmatic)
Navigate to Virtual Machines: In the Azure Portal, search for and select "Virtual machines."

Create a VM: Click "+ Create" and select "Azure virtual machine."

Basics Tab:

Select a subscription and create a new resource group.

Give the VM a name (e.g., MyWindowsServer).

Choose a region.

For the "Image," select "Windows Server 2022 Datacenter."

Choose a size.

Create an administrator username and a strong password.

Networking Tab: A VNet, subnet, and public IP will be created for you by default, which is fine for this demo.

Configure Inbound Ports: For "Public inbound ports," select "Allow selected ports." In the dropdown, choose RDP (3389). This automatically creates the required NSG rule. For better security, the NSG rule should be edited later to restrict the source IP.

Review and Create: Go to the "Review + create" tab. After validation passes, click "Create."

Connect to the VM: Once the deployment is complete, go to the resource page for your new VM. Click the "Connect" button and select "RDP."

Download RDP File: The portal provides a pre-configured RDP file. Click "Download RDP File."

Log In: Open the downloaded file. Your computer's Remote Desktop client will launch. Enter the username and password you created in step 3 to log in to your new Windows Server desktop in the cloud.

Scenario 61 (Azure): Hosting a Static Website with Azure Blob Storage 🌐
Title: Hosting a Static Website with Azure Blob Storage

Description: Learn how to host a simple, serverless static website (HTML, CSS, JS) using the static website hosting feature of Azure Storage.

User Prompt: "I want to host my simple portfolio website on Azure as cheaply as possible."

Services Involved:

Azure Storage (Blob)

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a user's browser. An arrow points from the browser to an icon representing an Azure Storage Account. Inside the storage account is a special container named $web. This container holds the website files (index.html, style.css). The storage account is configured for static website hosting, which provides a public web endpoint URL. The user accesses this URL to view the site.

Learning Steps (Manual/Programmatic)
Create a Storage Account: In the Azure Portal, search for "Storage accounts" and click "Create."

Configure Storage Account:

Create a new resource group.

Give the storage account a unique, lowercase name.

Choose a region.

For "Performance," select "Standard."

For "Redundancy," select "Locally-redundant storage (LRS)."

On the "Advanced" tab, ensure "Enable hierarchical namespace" is not checked.

Enable Static Website Hosting: After the storage account is created, navigate to its page. In the left menu, under "Data management," click "Static website."

Configure Hosting: Toggle the status to "Enabled." For the "Index document name," enter index.html. For the "Error document path," enter 404.html. Click "Save."

Note the Endpoint: The portal will now display the "Primary endpoint" URL for your static website. Copy this URL.

Upload Files: A special container named $web will now be visible in your storage account's "Containers" blade. Click on it. Use the "Upload" button to upload your index.html and any other CSS or JS files.

Test Your Website: Paste the primary endpoint URL you copied in step 5 into your web browser. Your static website should load.

Scenario 62 (Azure): Running a Simple Container with ACI 📦
Title: Running a Simple Container with Azure Container Instances (ACI)

Description: Learn how to run a single Docker container quickly and easily without needing to manage any virtual machines or orchestration platforms like Kubernetes.

User Prompt: "I have a Docker image. What's the fastest way to run it on Azure?"

Services Involved:

Azure Container Instances (ACI)

Level: Beginner

Estimated Time: 10 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows the Docker Hub (or another container registry) containing a public container image. An arrow points from the registry to the Azure Container Instances (ACI) service. The ACI service is shown pulling the image and running it as a single, isolated Container Group. ACI provides a public IP address and a FQDN, which a user is shown accessing to interact with the application running inside the container.

Learning Steps (Manual/Programmatic)
Navigate to ACI: In the Azure Portal, search for "Container instances" and click "Create."

Configure Basics:

Create a new resource group.

Give the container a name (e.g., my-simple-app).

Choose a region.

Configure Image Source:

For "Image source," select "Quickstart images."

From the dropdown, select the mcr.microsoft.com/azuredocs/aci-helloworld image. This is a simple pre-built web server.

For the "OS type," select Linux.

Configure Networking: In the "Networking" tab, you can configure the DNS name label. This becomes part of the container's public URL (e.g., my-simple-app.eastus.azurecontainer.io).

Review and Create: Go to the "Review + create" tab and click "Create." Deployment is very fast, usually taking less than a minute.

Verify the Container: Once deployed, go to the resource page for your container instance. In the "Overview" pane, you will find the fully qualified domain name (FQDN). Copy this FQDN and paste it into your web browser. You should see the "Welcome to Azure Container Instances!" page.

Scenario 63 (GCP): Launching a Linux VM on Compute Engine 🐧
Title: Launching a Linux VM on Compute Engine

Description: Learn the basics of Google Cloud's IaaS by launching a Debian Linux virtual machine and connecting to it securely using the browser-based SSH client.

User Prompt: "I need a basic Linux server on Google Cloud for development."

Services Involved:

Compute Engine

VPC Network

Level: Beginner

Estimated Time: 10 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows the Google Cloud VPC Network. Inside, there is a single Compute Engine VM Instance. A firewall rule is shown allowing inbound traffic on TCP port 22 (SSH) from Google's IAP (Identity-Aware Proxy) IP range. A user is shown clicking the "SSH" button in the GCP Console, which opens a secure SSH terminal directly in their web browser, connecting them to the VM without needing a public IP or managing SSH keys manually.

Learning Steps (Manual/Programmatic)
Navigate to Compute Engine: In the GCP Console, go to Compute Engine -> VM instances.

Create an Instance: Click "Create Instance."

Configure the VM:

Give the instance a name (e.g., dev-server-1).

Choose a region and zone.

For the "Machine configuration," choose a small, cost-effective series like E2 and machine type e2-micro.

Configure Boot Disk and Firewall:

In the "Boot disk" section, the default Debian Linux image is fine.

In the "Firewall" section, check the box to "Allow HTTP traffic." This creates a firewall rule to allow web traffic on port 80.

Create the VM: Click "Create." The instance will be provisioned in about a minute.

Connect via SSH: In the list of VM instances, find your new VM. In the "Connect" column, click the SSH button.

Use the Terminal: A new browser window will open, establishing a secure SSH connection to your VM. You are now logged into the command line of your new Linux server. You can run commands like sudo apt-get update or ls -l.

Test Web Server (Optional): In the SSH terminal, install a web server by running sudo apt-get install -y nginx. Once installed, find the "External IP" of your VM in the GCP console and paste it into a new browser tab. You should see the default NGINX welcome page.

Scenario 64 (GCP): Creating a Cloud SQL Database 🐬
Title: Creating a Cloud SQL Database

Description: Learn how to create and connect to a fully managed MySQL database using Cloud SQL, Google Cloud's managed relational database service.

User Prompt: "I need a managed MySQL database on GCP for my application."

Services Involved:

Cloud SQL

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Medium

Architecture Diagram (PNG) Description
The diagram shows a Cloud SQL Instance running the MySQL engine. It is configured with a "Public IP" address. The instance's "Authorized Networks" setting is shown, acting as a firewall. It lists a specific IP address (the user's office/home IP) as an allowed source. A user is shown connecting from their local computer using a database client (like MySQL Workbench) to the Cloud SQL instance's public IP. The connection succeeds because their IP is authorized.

Learning Steps (Manual/Programmatic)
Navigate to Cloud SQL: In the GCP Console, go to Databases -> SQL.

Create an Instance: Click "Create instance."

Choose an Engine: Choose "MySQL" as your database engine.

Configure the Instance:

Provide an "Instance ID" (e.g., my-app-database).

Generate a strong password for the root user.

Choose a region and zone.

Customize Configuration: Click "Customize your instance."

Under "Machine type," choose a lightweight configuration like "db-n1-standard-1" to start.

Under "Connectivity," select "Public IP." Click "+ Add network." Enter your current public IP address to authorize yourself to connect. You can find your IP by searching "what is my IP" in Google.

Create and Wait: Click "Create instance." It will take several minutes to provision.

Connect to the Database: Once the instance is ready, select it to view its overview page. Find the "Public IP address."

Use a Database Client: Use a local SQL client (like MySQL Workbench, DBeaver, or the mysql command line). Use the Public IP as the host, root as the username, and the password you created to establish a connection to your new managed database.

Scenario 65 (GCP): Storing and Sharing Files with Cloud Storage 🧺
Title: Storing and Sharing Files with Cloud Storage

Description: Learn the basics of Google Cloud Storage by creating a bucket, uploading objects, and securely sharing an object with a specific user using signed URLs.

User Prompt: "I need to store private user documents and occasionally generate a secure, temporary download link for them."

Services Involved:

Cloud Storage

IAM (implicitly)

Level: Beginner

Estimated Time: 15 minutes

Cost Level: Low

Architecture Diagram (PNG) Description
The diagram shows a Cloud Storage bucket containing private files. An application server is shown with an associated IAM Service Account. This service account has permissions to access the bucket. A user requests a download link from the application. The application uses its service account credentials to ask Cloud Storage to generate a Signed URL for a specific private file. This URL has a short expiration time. The application gives this temporary URL to the user, who can then use it to download the file directly from Cloud Storage before it expires.

Learning Steps (Manual/Programmatic)
Prerequisites: Have the gcloud command-line tool installed and configured.

Create a Bucket: In the GCP Console, go to Cloud Storage -> Buckets. Click "Create."

Give the bucket a globally unique name.

Choose a location and storage class (Standard is fine).

For "Access control," choose "Uniform." Uncheck the box for "Enforce public access prevention" for this demo, though in production you'd leave it checked.

Upload a Private File: Create the bucket. Upload a sample file (e.g., private-document.pdf). By default, it's not publicly accessible.

Verify Private Access: Click on the uploaded file. The "Public URL" will have a lock icon next to it. If you try to open it, you'll get an access denied error.

Generate a Signed URL (via CLI): A real application would use an SDK, but the gcloud CLI is a great way to learn. Open your terminal. Run the following command, replacing the bucket and object names:

Bash

gcloud storage sign-url --expires-in 5m gs://your-unique-bucket-name/private-document.pdf
Use the Signed URL: The command will output a long URL containing authentication parameters and an expiration timestamp. This URL is a temporary, secure credential to access the object.

Test the Link: Copy the entire signed URL and paste it into your browser. The private document will download successfully. Wait for 5 minutes to pass and try the same URL again. This time, you will get an "access denied" error because the URL has expired, demonstrating the security of the method.



--------------

