# Batch 1: BASIC LEVEL (1-2 Difficulty)

---
# 1. Single Pod Running a Static Website
apiVersion: v1
kind: Pod
metadata:
  name: static-website
  labels:
    app: nginx-static
spec:
  containers:
  - name: nginx
    image: nginx:alpine
    volumeMounts:
    - name: html
      mountPath: /usr/share/nginx/html
    ports:
    - containerPort: 80
  volumes:
  - name: html
    configMap:
      name: static-html
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: static-html
  labels:
    app: nginx-static
data:
  index.html: |
    <html><body><h1>Hello from NGINX Static Site</h1></body></html>

---
# 2. Pod Exposed via NodePort Service
apiVersion: v1
kind: Service
metadata:
  name: nginx-nodeport
spec:
  type: NodePort
  selector:
    app: nginx-static
  ports:
    - port: 80
      targetPort: 80
      nodePort: 30080

---
# 3. Multiple Pods Load Balanced by ClusterIP
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: nginx-clusterip
  template:
    metadata:
      labels:
        app: nginx-clusterip
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-clusterip-service
spec:
  type: ClusterIP
  selector:
    app: nginx-clusterip
  ports:
    - port: 80
      targetPort: 80

---
# 4. Pod with ConfigMap Injection
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  labels:
    app: config-demo
data:
  APP_MODE: production
  LOG_LEVEL: debug
---
apiVersion: v1
kind: Pod
metadata:
  name: config-demo
spec:
  containers:
  - name: demo
    image: busybox
    command: ["sh", "-c", "env; sleep 3600"]
    envFrom:
    - configMapRef:
        name: app-config

---
# 5. Basic Ingress Routing
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: simple-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - http:
      paths:
      - path: /nginx
        pathType: Prefix
        backend:
          service:
            name: nginx-nodeport
            port:
              number: 80

---
# 6. ClusterIP Not Exposed
apiVersion: v1
kind: Service
metadata:
  name: internal-service
spec:
  type: ClusterIP
  selector:
    app: internal-app
  ports:
    - port: 8080
---
apiVersion: v1
kind: Pod
metadata:
  name: internal-app
  labels:
    app: internal-app
spec:
  containers:
  - name: app
    image: hashicorp/http-echo
    args:
    - "-text=Hello Internal"
    ports:
    - containerPort: 8080

---
# 7. LoadBalancer Type Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lb-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: lb-app
  template:
    metadata:
      labels:
        app: lb-app
    spec:
      containers:
      - name: app
        image: hashicorp/http-echo
        args: ["-text=Hello from LoadBalancer"]
        ports:
        - containerPort: 5678
---
apiVersion: v1
kind: Service
metadata:
  name: lb-service
spec:
  type: LoadBalancer
  selector:
    app: lb-app
  ports:
    - port: 80
      targetPort: 5678


---------
# Batch 2: INTERMEDIATE, ADVANCED & PRODUCTION LEVEL

---
# 8. Frontend + Backend (2 Services)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: python:3.9
        command: ["python", "-m", "http.server", "5000"]
        ports:
        - containerPort: 5000
---
apiVersion: v1
kind: Service
metadata:
  name: backend
spec:
  selector:
    app: backend
  ports:
  - port: 5000
    targetPort: 5000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
  template:
    metadata:
      labels:
        app: frontend
    spec:
      containers:
      - name: frontend
        image: nginx
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: frontend
spec:
  selector:
    app: frontend
  ports:
  - port: 80
    targetPort: 80

---
# 9. Pod with PersistentVolume
apiVersion: v1
kind: PersistentVolume
metadata:
  name: local-pv
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: "/mnt/data"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: local-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: Pod
metadata:
  name: pv-demo
spec:
  containers:
  - name: app
    image: busybox
    command: ["sh", "-c", "echo Hello > /data/test.txt; sleep 3600"]
    volumeMounts:
    - mountPath: "/data"
      name: storage
  volumes:
  - name: storage
    persistentVolumeClaim:
      claimName: local-pvc

---
# 10. Horizontal Pod Autoscaler Triggers
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: hpa-demo
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 50

---
# 11. Microservices App with Istio + Observability
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-a
spec:
  replicas: 2
  selector:
    matchLabels:
      app: service-a
  template:
    metadata:
      labels:
        app: service-a
    spec:
      containers:
      - name: app
        image: hashicorp/http-echo
        args: ["-text=Service A"]
        ports:
        - containerPort: 5678
---
apiVersion: v1
kind: Service
metadata:
  name: service-a
spec:
  selector:
    app: service-a
  ports:
  - port: 80
    targetPort: 5678

---
# 12. Enterprise GitOps CI/CD Pipeline (ArgoCD)
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: guestbook
  namespace: argocd
spec:
  destination:
    namespace: default
    server: https://kubernetes.default.svc
  project: default
  source:
    repoURL: https://github.com/argoproj/argocd-example-apps
    targetRevision: HEAD
    path: guestbook
  syncPolicy:
    automated:
      prune: true
      selfHeal: true

---
# 13. Highly Available Web App with HPA + NGINX + Redis
apiVersion: apps/v1
kind: Deployment
metadata:
  name: webapp
spec:
  replicas: 2
  selector:
    matchLabels:
      app: webapp
  template:
    metadata:
      labels:
        app: webapp
    spec:
      containers:
      - name: web
        image: nginx
        ports:
        - containerPort: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis
        ports:
        - containerPort: 6379
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
  - port: 6379
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: webapp-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: webapp
  minReplicas: 2
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60

---
# 14. Multi-Cluster with Global Load Balancing
# NOTE: Requires external DNS/GSLB - only sketch here
# Use ExternalDNS, Istio east-west gateway, and CoreDNS config across clusters.
# This is typically not a single YAML but a set of scripts and infra-as-code configs.

---
# 15. Stateful Database with Backup/Restore + Read Replicas
# Placeholder using StatefulSet for PostgreSQL - assumes sidecar for backup
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
spec:
  serviceName: "postgres"
  replicas: 2
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:14
        env:
        - name: POSTGRES_PASSWORD
          value: example
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: pgdata
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: pgdata
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 1Gi

---
# 16. E-commerce Platform with Kafka + MongoDB + Elastic
# Placeholder - full setup involves multiple Helm charts or operators
# This file would include deployments for Kafka (Bitnami), MongoDB (ReplicaSet), and ElasticSearch (Elastic Operator)
# Recommend using Helm or Kustomize for production deployment.

---
# 17. ML Training Pipeline on K8s with GPUs
# Requires GPU-enabled nodes
apiVersion: v1
kind: Pod
metadata:
  name: ml-train
spec:
  containers:
  - name: trainer
    image: tensorflow/tensorflow:latest-gpu
    resources:
      limits:
        nvidia.com/gpu: 1
    command: ["python", "train.py"]

---
# 18. Zero Trust Cluster with OPA Gatekeeper + NetworkPolicy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

# Gatekeeper constraints and templates should be applied separately.

---
# 19. Event-driven Autoscaling with KEDA + RabbitMQ
# Assume KEDA installed, deployment triggers on queue length
apiVersion: apps/v1
kind: Deployment
metadata:
  name: worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: worker
  template:
    metadata:
      labels:
        app: worker
    spec:
      containers:
      - name: worker
        image: myqueueprocessor:latest
---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: rabbitmq-scaledobject
spec:
  scaleTargetRef:
    name: worker
  triggers:
  - type: rabbitmq
    metadata:
      queueName: jobs
      host: RabbitMQConnectionString

---
# 20. Full Observability Stack with Logging, Tracing, Metrics
# Typically includes Prometheus, Grafana, Loki, Jaeger
# Use Helm charts (prometheus-community/kube-prometheus-stack, grafana/loki-stack, jaegertracing/jaeger)
# Below is only a placeholder config
apiVersion: v1
kind: Namespace
metadata:
  name: observability


--------
# 21. Misconfigured Ingress Path
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bad-ingress
spec:
  rules:
  - http:
      paths:
      - path: /wrongpath
        pathType: Prefix
        backend:
          service:
            name: myservice
            port:
              number: 80
# Assume app expects "/app" but ingress routes "/wrongpath"

---
# 22. Service Without Matching Pods
apiVersion: v1
kind: Service
metadata:
  name: orphan-service
spec:
  selector:
    app: non-existent-app
  ports:
  - port: 80
# No pods with label "app=non-existent-app" exist

---
# 23. NodePort Misused (wrong port range)
apiVersion: v1
kind: Service
metadata:
  name: invalid-nodeport
spec:
  type: NodePort
  selector:
    app: test
  ports:
  - port: 80
    targetPort: 80
    nodePort: 20000  # Valid range is 30000–32767 unless reconfigured

---
# 24. Readiness Probe Fails
apiVersion: v1
kind: Pod
metadata:
  name: probe-fail
spec:
  containers:
  - name: app
    image: nginx
    readinessProbe:
      httpGet:
        path: /invalid
        port: 80
      initialDelaySeconds: 3
      periodSeconds: 5

---
# 25. CrashLoopBackOff Pod
apiVersion: v1
kind: Pod
metadata:
  name: crashing-pod
spec:
  containers:
  - name: crash
    image: busybox
    command: ["sh", "-c", "exit 1"]

---
# 26. ConfigMap Missing
apiVersion: v1
kind: Pod
metadata:
  name: missing-configmap
spec:
  containers:
  - name: app
    image: nginx
    volumeMounts:
    - name: config
      mountPath: /etc/config
  volumes:
  - name: config
    configMap:
      name: nonexistent-config

---
# 27. Secret Not Mounted
apiVersion: v1
kind: Pod
metadata:
  name: secret-not-mounted
spec:
  containers:
  - name: app
    image: nginx
    envFrom:
    - secretRef:
        name: missing-secret

---
# 28. ImagePullBackOff
apiVersion: v1
kind: Pod
metadata:
  name: bad-image
spec:
  containers:
  - name: app
    image: nonexisting/image:latest

---
# 29. Pod Pending - No Node Match
apiVersion: v1
kind: Pod
metadata:
  name: node-match-fail
spec:
  containers:
  - name: app
    image: nginx
  nodeSelector:
    disktype: ssd  # No node has this label


