export interface CloudScenario {
  id: string;
  title: string;
  prompt: string;
  level: 'beginner' | 'intermediate' | 'expert';
  provider: 'aws' | 'azure' | 'gcp' | 'multi-cloud' | 'hybrid';
  category: 'web-hosting' | 'serverless' | 'containers' | 'data' | 'ml-ai' | 'security' | 'devops' | 'iot' | 'enterprise';
  services: string[];
  architecture: string;
  exportFormats: string[];
  estimatedTime: string;
  costLevel: 'low' | 'medium' | 'high';
  tags: string[];
  description: string;
}

export const cloudScenarios: CloudScenario[] = [
  // BEGINNER SCENARIOS
  {
    id: 'aws-static-website',
    title: 'Static Website Hosting on AWS',
    prompt: 'Build me a simple architecture to host a static website on AWS.',
    level: 'beginner',
    provider: 'aws',
    category: 'web-hosting',
    services: ['S3', 'CloudFront', 'Route 53', 'IAM'],
    architecture: 'S3 (static hosting), CloudFront (CDN), Route 53, IAM policy',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['static', 'cdn', 'dns', 'beginner-friendly'],
    description: 'Learn the fundamentals of hosting static websites on AWS with global CDN distribution.'
  },
  {
    id: 'azure-vm-hosting',
    title: 'Basic VM Hosting on Azure',
    prompt: 'I want to deploy a web server on Azure with basic security and availability.',
    level: 'beginner',
    provider: 'azure',
    category: 'web-hosting',
    services: ['Virtual Machine', 'Load Balancer', 'NSG', 'Storage Account'],
    architecture: 'Azure Virtual Machine, Azure Load Balancer, NSG, Storage Account',
    exportFormats: ['PNG', 'ARM Template'],
    estimatedTime: '20 minutes',
    costLevel: 'medium',
    tags: ['vm', 'load-balancer', 'security', 'availability'],
    description: 'Deploy and secure virtual machines on Azure with proper networking and load balancing.'
  },
  {
    id: 'gcp-cloud-function',
    title: 'GCP Cloud Functions with Pub/Sub',
    prompt: 'Trigger a GCP Cloud Function from a Pub/Sub event.',
    level: 'beginner',
    provider: 'gcp',
    category: 'serverless',
    services: ['Cloud Functions', 'Pub/Sub'],
    architecture: 'Pub/Sub → Cloud Function',
    exportFormats: ['PNG', 'Deployment Manager'],
    estimatedTime: '10 minutes',
    costLevel: 'low',
    tags: ['serverless', 'event-driven', 'messaging'],
    description: 'Understand event-driven serverless computing with Google Cloud Functions and Pub/Sub.'
  },

  // INTERMEDIATE SCENARIOS
  {
    id: 'gcp-scalable-webapp',
    title: 'Scalable Web App on GCP',
    prompt: 'Create a scalable architecture for a React web app with a Node.js backend on GCP.',
    level: 'intermediate',
    provider: 'gcp',
    category: 'web-hosting',
    services: ['Cloud Run', 'Cloud Storage', 'Firestore', 'Cloud Load Balancer', 'Cloud Build'],
    architecture: 'Cloud Run or GKE, Cloud Storage, Firestore, Cloud Load Balancer',
    exportFormats: ['PNG', 'YAML', 'Terraform'],
    estimatedTime: '45 minutes',
    costLevel: 'medium',
    tags: ['scalable', 'containers', 'ci-cd', 'full-stack'],
    description: 'Build a production-ready scalable web application with automated CI/CD on Google Cloud.'
  },
  {
    id: 'aws-serverless-api',
    title: 'Serverless API on AWS',
    prompt: 'Design a serverless REST API on AWS.',
    level: 'intermediate',
    provider: 'aws',
    category: 'serverless',
    services: ['API Gateway', 'Lambda', 'DynamoDB', 'CloudWatch', 'Cognito'],
    architecture: 'API Gateway → Lambda → DynamoDB',
    exportFormats: ['PNG', 'CloudFormation', 'SAM'],
    estimatedTime: '40 minutes',
    costLevel: 'low',
    tags: ['serverless', 'api', 'nosql', 'authentication'],
    description: 'Create a fully serverless REST API with authentication, logging, and monitoring.'
  },
  {
    id: 'azure-kubernetes-cicd',
    title: 'Kubernetes Deployment with CI/CD',
    prompt: 'Build me a Kubernetes-based architecture for microservices with CI/CD on Azure.',
    level: 'intermediate',
    provider: 'azure',
    category: 'containers',
    services: ['AKS', 'Azure Container Registry', 'Azure DevOps', 'Ingress Controller', 'Prometheus', 'Grafana'],
    architecture: 'AKS cluster, Azure Container Registry, Azure DevOps pipelines',
    exportFormats: ['YAML', 'PNG', 'Helm Charts'],
    estimatedTime: '60 minutes',
    costLevel: 'medium',
    tags: ['kubernetes', 'microservices', 'ci-cd', 'monitoring'],
    description: 'Deploy microservices on Kubernetes with complete CI/CD pipeline and monitoring stack.'
  },

  // EXPERT SCENARIOS
  {
    id: 'aws-multi-region-dr',
    title: 'Multi-Region Failover (AWS)',
    prompt: 'Show a multi-region disaster recovery setup for a production workload on AWS.',
    level: 'expert',
    provider: 'aws',
    category: 'enterprise',
    services: ['RDS', 'S3', 'Route 53', 'CloudFormation', 'Lambda'],
    architecture: 'RDS with cross-region read replica, S3 cross-region replication',
    exportFormats: ['CloudFormation', 'PNG', 'Cost Analysis'],
    estimatedTime: '90 minutes',
    costLevel: 'high',
    tags: ['disaster-recovery', 'multi-region', 'high-availability', 'enterprise'],
    description: 'Design enterprise-grade disaster recovery with automated failover across AWS regions.'
  },
  {
    id: 'azure-data-lake',
    title: 'Data Lake & Analytics Pipeline (Azure)',
    prompt: 'Create a data lake and ETL pipeline for big data processing on Azure.',
    level: 'expert',
    provider: 'azure',
    category: 'data',
    services: ['Data Lake Gen2', 'Data Factory', 'Synapse Analytics', 'Blob Storage', 'Databricks'],
    architecture: 'Azure Data Lake Gen2, Data Factory, Synapse Analytics',
    exportFormats: ['PNG', 'ARM Template', 'Data Flow Diagram'],
    estimatedTime: '120 minutes',
    costLevel: 'high',
    tags: ['big-data', 'analytics', 'etl', 'machine-learning'],
    description: 'Build a comprehensive data lake and analytics pipeline for enterprise big data processing.'
  },
  {
    id: 'gcp-iot-realtime',
    title: 'Real-Time IoT Architecture (GCP)',
    prompt: 'Design an IoT architecture for thousands of devices sending data in real-time to GCP.',
    level: 'expert',
    provider: 'gcp',
    category: 'iot',
    services: ['IoT Core', 'Pub/Sub', 'Dataflow', 'BigQuery', 'Cloud Functions'],
    architecture: 'IoT Core, Pub/Sub, Dataflow, BigQuery',
    exportFormats: ['PNG', 'Terraform', 'Monitoring Dashboard'],
    estimatedTime: '100 minutes',
    costLevel: 'high',
    tags: ['iot', 'real-time', 'streaming', 'big-data'],
    description: 'Handle massive IoT data streams with real-time processing and analytics on Google Cloud.'
  },
  {
    id: 'saas-multi-tenant',
    title: 'SaaS Multi-Tenant Architecture',
    prompt: 'I need a multi-tenant SaaS architecture that supports scaling, isolation, and monitoring.',
    level: 'expert',
    provider: 'multi-cloud',
    category: 'enterprise',
    services: ['Kubernetes', 'OAuth', 'Prometheus', 'ELK Stack', 'Load Balancers'],
    architecture: 'Tenant isolation with Kubernetes namespaces',
    exportFormats: ['YAML', 'PNG', 'Security Audit'],
    estimatedTime: '150 minutes',
    costLevel: 'high',
    tags: ['saas', 'multi-tenant', 'kubernetes', 'security', 'monitoring'],
    description: 'Design a secure, scalable multi-tenant SaaS platform with proper isolation and monitoring.'
  },
  {
    id: 'aws-ml-secure',
    title: 'Secure ML Deployment on AWS',
    prompt: 'Deploy a machine learning model with secure APIs and role-based access control on AWS.',
    level: 'expert',
    provider: 'aws',
    category: 'ml-ai',
    services: ['SageMaker', 'API Gateway', 'Lambda', 'IAM', 'CloudTrail'],
    architecture: 'SageMaker endpoint, API Gateway, Lambda auth (JWT), IAM roles',
    exportFormats: ['CloudFormation', 'PNG', 'Cost Estimation'],
    estimatedTime: '80 minutes',
    costLevel: 'medium',
    tags: ['machine-learning', 'security', 'api', 'rbac'],
    description: 'Deploy ML models securely with proper authentication, authorization, and audit trails.'
  },

  // SECURITY & COMPLIANCE
  {
    id: 'azure-zero-trust',
    title: 'Zero Trust Architecture on Azure',
    prompt: 'Design a zero trust network architecture on Azure for internal applications with identity enforcement and segmentation.',
    level: 'expert',
    provider: 'azure',
    category: 'security',
    services: ['Azure AD', 'Conditional Access', 'Azure Firewall', 'Azure Bastion', 'NSGs'],
    architecture: 'Azure AD, Conditional Access, Azure Firewall, Azure Bastion',
    exportFormats: ['PNG', 'Security Policy', 'ARM Template'],
    estimatedTime: '110 minutes',
    costLevel: 'high',
    tags: ['zero-trust', 'security', 'identity', 'network-segmentation'],
    description: 'Implement zero trust security model with comprehensive identity and network controls.'
  },
  {
    id: 'aws-pci-ecommerce',
    title: 'PCI-Compliant E-commerce App on AWS',
    prompt: 'Build a PCI-DSS compliant architecture for an e-commerce app on AWS.',
    level: 'expert',
    provider: 'aws',
    category: 'security',
    services: ['ALB', 'ECS Fargate', 'RDS', 'WAF', 'CloudTrail', 'GuardDuty', 'IAM'],
    architecture: 'ALB → ECS (Fargate), RDS with encryption',
    exportFormats: ['CloudFormation', 'PNG', 'Compliance Checklist'],
    estimatedTime: '130 minutes',
    costLevel: 'high',
    tags: ['pci-compliance', 'e-commerce', 'security', 'encryption'],
    description: 'Build PCI-DSS compliant e-commerce platform with comprehensive security controls.'
  },

  // MACHINE LEARNING & AI
  {
    id: 'gcp-ml-pipeline',
    title: 'End-to-End ML Pipeline on GCP',
    prompt: 'Show an end-to-end machine learning workflow on GCP from data ingestion to model training and deployment.',
    level: 'expert',
    provider: 'gcp',
    category: 'ml-ai',
    services: ['BigQuery', 'Dataflow', 'AI Platform Training', 'Model Registry', 'Cloud Run'],
    architecture: 'BigQuery → Dataflow → AI Platform Training → Model Registry → Cloud Run',
    exportFormats: ['PNG', 'Terraform', 'Kubeflow Pipeline'],
    estimatedTime: '140 minutes',
    costLevel: 'high',
    tags: ['machine-learning', 'mlops', 'data-pipeline', 'model-deployment'],
    description: 'Complete MLOps pipeline from data ingestion to model deployment and monitoring.'
  },
  {
    id: 'azure-ml-autoscale',
    title: 'Real-Time ML Inference with Auto-Scaling',
    prompt: 'Design a low-latency ML inference architecture on Azure that auto-scales based on request load.',
    level: 'expert',
    provider: 'azure',
    category: 'ml-ai',
    services: ['AKS', 'KEDA', 'Azure ML', 'ONNX', 'Azure Monitor'],
    architecture: 'Azure Kubernetes Service + KEDA',
    exportFormats: ['YAML', 'PNG', 'Performance Metrics'],
    estimatedTime: '95 minutes',
    costLevel: 'medium',
    tags: ['machine-learning', 'auto-scaling', 'low-latency', 'kubernetes'],
    description: 'Deploy ML models with automatic scaling based on demand and performance requirements.'
  },

  // MULTI-CLOUD & HYBRID
  {
    id: 'hybrid-aws-onprem',
    title: 'Hybrid Cloud with On-Prem and AWS',
    prompt: 'Design a hybrid cloud architecture with an on-prem data center connected to AWS.',
    level: 'expert',
    provider: 'hybrid',
    category: 'enterprise',
    services: ['Direct Connect', 'VPN', 'Transit Gateway', 'Active Directory', 'CloudWatch'],
    architecture: 'Direct Connect, VPN, Transit Gateway',
    exportFormats: ['PNG', 'Network Diagram', 'CloudFormation'],
    estimatedTime: '120 minutes',
    costLevel: 'high',
    tags: ['hybrid-cloud', 'on-premises', 'networking', 'identity-federation'],
    description: 'Connect on-premises infrastructure with AWS using secure, high-performance networking.'
  },
  {
    id: 'multi-cloud-redundancy',
    title: 'Multi-Cloud Redundancy (AWS + GCP)',
    prompt: 'Build a highly available web app across AWS and GCP with DNS-based routing and shared storage.',
    level: 'expert',
    provider: 'multi-cloud',
    category: 'enterprise',
    services: ['AWS ALB', 'GCP Load Balancer', 'Route 53', 'Cloud DNS', 'S3', 'GCS'],
    architecture: 'AWS ALB + GCP Load Balancer',
    exportFormats: ['PNG', 'Multi-Cloud Diagram', 'Cost Comparison'],
    estimatedTime: '160 minutes',
    costLevel: 'high',
    tags: ['multi-cloud', 'high-availability', 'dns-routing', 'redundancy'],
    description: 'Achieve ultimate availability by distributing workloads across multiple cloud providers.'
  },

  // ADDITIONAL SCENARIOS FROM SCENARION.TXT
  {
    id: 'azure-serverless-api',
    title: 'Scalable Serverless API on Azure',
    prompt: 'Build a scalable, low-cost API for my mobile app on Azure.',
    level: 'intermediate',
    provider: 'azure',
    category: 'serverless',
    services: ['Azure Functions', 'API Management', 'Cosmos DB', 'Application Insights'],
    architecture: 'Azure Functions + API Management + Cosmos DB + Application Insights',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['serverless', 'api', 'mobile', 'cosmos-db', 'monitoring'],
    description: 'Build a highly scalable, event-driven, and cost-efficient REST API backend using Azure\'s core serverless components for modern mobile applications.'
  },
  {
    id: 'gcp-cloud-run-container',
    title: 'Containerized Web App on GCP Cloud Run',
    prompt: 'Deploy my Node.js Docker container as a scalable web service on GCP.',
    level: 'beginner',
    provider: 'gcp',
    category: 'containers',
    services: ['Cloud Run', 'Artifact Registry', 'Cloud Build', 'IAM'],
    architecture: 'Cloud Run + Artifact Registry + Cloud Build + IAM',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['containers', 'serverless', 'docker', 'nodejs', 'auto-scaling'],
    description: 'Deploy a containerized application as a fully managed, auto-scaling serverless web service on Google Cloud Run with zero server management.'
  },
  {
    id: 'aws-realtime-data-processing',
    title: 'Real-time Data Processing Pipeline',
    prompt: 'Create a system to analyze real-time clickstream data from my website.',
    level: 'intermediate',
    provider: 'aws',
    category: 'data',
    services: ['Kinesis Data Streams', 'Lambda', 'S3', 'IAM'],
    architecture: 'Kinesis Data Streams + Lambda + S3 + IAM',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '30 minutes',
    costLevel: 'medium',
    tags: ['real-time', 'streaming', 'analytics', 'lambda', 'data-lake'],
    description: 'Build an end-to-end, serverless pipeline to ingest, process, and store real-time streaming data on AWS for analytics and insights.'
  },
  {
    id: 'gcp-private-gke-cluster',
    title: 'Private GKE Cluster with Cloud SQL',
    prompt: 'Set up a secure Kubernetes environment that is not exposed to the internet and can connect to a Postgres database.',
    level: 'expert',
    provider: 'gcp',
    category: 'containers',
    services: ['GKE', 'VPC Network', 'Cloud SQL', 'Cloud NAT', 'IAM'],
    architecture: 'Private GKE Cluster + Cloud SQL + VPC + Cloud NAT',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '60 minutes',
    costLevel: 'high',
    tags: ['kubernetes', 'private', 'security', 'database', 'vpc'],
    description: 'Deploy a secure, production-grade microservices application on a private Google Kubernetes Engine (GKE) cluster that communicates securely with a managed SQL database.'
  },
  {
    id: 'azure-cicd-containers',
    title: 'CI/CD Pipeline for Containers on Azure',
    prompt: 'I want to automatically update my web app whenever I push code to my repo.',
    level: 'intermediate',
    provider: 'azure',
    category: 'devops',
    services: ['Azure App Service', 'Azure Container Registry', 'Azure DevOps Pipelines', 'Azure Monitor'],
    architecture: 'Azure DevOps + ACR + App Service + Azure Monitor',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '45 minutes',
    costLevel: 'medium',
    tags: ['cicd', 'containers', 'devops', 'automation', 'monitoring'],
    description: 'Automate the build and deployment of a containerized application from a Git repository to Azure App Service using Azure DevOps for seamless continuous delivery.'
  },
  {
    id: 'aws-serverless-data-lake',
    title: 'AWS Serverless Data Lake Querying',
    prompt: 'I have TBs of log files in S3. I need an easy way to run SQL queries on them.',
    level: 'intermediate',
    provider: 'aws',
    category: 'data',
    services: ['S3', 'AWS Glue Crawler', 'AWS Glue Data Catalog', 'Amazon Athena'],
    architecture: 'S3 + Glue Crawler + Glue Data Catalog + Athena',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['data-lake', 'sql', 'serverless', 'analytics', 'pay-per-query'],
    description: 'Build a system to catalog raw data in S3 and run complex SQL queries on it without managing any servers or data warehouse infrastructure.'
  },
  {
    id: 'gcp-document-processing',
    title: 'Intelligent Document Processing on GCP',
    prompt: 'I need to automatically scan invoices and extract the total amount and due date.',
    level: 'expert',
    provider: 'gcp',
    category: 'ml-ai',
    services: ['Cloud Storage', 'Cloud Functions', 'Document AI', 'BigQuery'],
    architecture: 'Cloud Storage + Cloud Functions + Document AI + BigQuery',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '50 minutes',
    costLevel: 'medium',
    tags: ['ai', 'document-processing', 'ocr', 'automation', 'bigquery'],
    description: 'Build a serverless pipeline that automatically extracts structured data from uploaded documents (like invoices or receipts) using Google Cloud\'s AI services.'
  },
  {
    id: 'aws-multi-region-active-active',
    title: 'Multi-Region Active-Active Web Application',
    prompt: 'Build me a bulletproof global application that never goes down.',
    level: 'expert',
    provider: 'aws',
    category: 'enterprise',
    services: ['Route 53', 'CloudFront', 'Application Load Balancer', 'EC2 Auto Scaling', 'DynamoDB Global Tables'],
    architecture: 'Route 53 + CloudFront + ALB + Auto Scaling + DynamoDB Global Tables',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '90 minutes',
    costLevel: 'high',
    tags: ['multi-region', 'high-availability', 'global', 'fault-tolerant', 'auto-scaling'],
    description: 'Design a highly available and fault-tolerant architecture that serves traffic from multiple AWS regions simultaneously, providing low latency for global users and resilience against regional outages.'
  },
  {
    id: 'azure-iot-monitoring',
    title: 'Azure IoT Data Ingestion and Monitoring',
    prompt: 'I need to collect temperature data from sensors and show it on a dashboard.',
    level: 'expert',
    provider: 'azure',
    category: 'iot',
    services: ['IoT Hub', 'Stream Analytics', 'Azure Cosmos DB', 'Power BI'],
    architecture: 'IoT Hub + Stream Analytics + Cosmos DB + Power BI',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '60 minutes',
    costLevel: 'medium',
    tags: ['iot', 'real-time', 'monitoring', 'dashboard', 'telemetry'],
    description: 'Build a scalable solution to ingest telemetry data from millions of IoT devices, process it in real-time, and store it for analysis and visualization.'
  },
  {
    id: 'gcp-big-data-etl',
    title: 'Big Data ETL Pipeline with GCP Dataproc',
    prompt: 'I need to process terabytes of raw data nightly using a Spark job.',
    level: 'expert',
    provider: 'gcp',
    category: 'data',
    services: ['Cloud Storage', 'Dataproc', 'Cloud Composer', 'BigQuery'],
    architecture: 'Cloud Storage + Dataproc + Cloud Composer + BigQuery',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '75 minutes',
    costLevel: 'high',
    tags: ['big-data', 'etl', 'spark', 'batch-processing', 'data-warehouse'],
    description: 'Design a batch processing pipeline to run large-scale data transformations using a managed Apache Spark cluster on Google Cloud for enterprise data processing.'
  },
  {
    id: 'aws-secure-bastion-host',
    title: 'Secure Bastion Host Access on AWS',
    prompt: 'How can I securely SSH into my private EC2 instances without exposing them to the internet?',
    level: 'intermediate',
    provider: 'aws',
    category: 'security',
    services: ['VPC', 'EC2', 'Systems Manager', 'IAM'],
    architecture: 'VPC + EC2 + Systems Manager Session Manager + IAM',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '40 minutes',
    costLevel: 'low',
    tags: ['security', 'bastion', 'ssh', 'private-access', 'systems-manager'],
    description: 'Implement a secure method to access private resources using a hardened bastion host and AWS Systems Manager for shell access, eliminating the need for open SSH ports.'
  },
  {
    id: 'gcp-media-transcoding',
    title: 'GCP Media Streaming and Transcoding Pipeline',
    prompt: 'When I upload a high-res video, I need it converted into different qualities for my streaming app.',
    level: 'expert',
    provider: 'gcp',
    category: 'enterprise',
    services: ['Cloud Storage', 'Cloud Functions', 'Transcoder API', 'Cloud CDN'],
    architecture: 'Cloud Storage + Cloud Functions + Transcoder API + Cloud CDN',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '50 minutes',
    costLevel: 'medium',
    tags: ['media', 'transcoding', 'streaming', 'cdn', 'video-processing'],
    description: 'Create an automated pipeline that transcodes uploaded video files into multiple formats and bitrates suitable for adaptive streaming on web and mobile devices.'
  },
  {
    id: 'aws-api-gateway-vpc-link',
    title: 'AWS API Gateway with VPC Link',
    prompt: 'I need to create a public REST API for a service running on private servers.',
    level: 'expert',
    provider: 'aws',
    category: 'enterprise',
    services: ['API Gateway', 'VPC Link', 'Network Load Balancer', 'EC2', 'VPC'],
    architecture: 'API Gateway + VPC Link + NLB + Private EC2 + VPC',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '60 minutes',
    costLevel: 'medium',
    tags: ['api-gateway', 'vpc-link', 'private', 'load-balancer', 'security'],
    description: 'Expose a private service running on EC2 instances within a VPC to the public through a secure, managed API Gateway without exposing the instances to the internet.'
  },
  {
    id: 'azure-blue-green-deployment',
    title: 'Azure Blue/Green Deployment for App Service',
    prompt: 'How can I update my production web app without any downtime for my users?',
    level: 'intermediate',
    provider: 'azure',
    category: 'devops',
    services: ['Azure App Service', 'App Service Plan', 'Deployment Slots'],
    architecture: 'Azure App Service + Deployment Slots (Blue/Green)',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '30 minutes',
    costLevel: 'medium',
    tags: ['blue-green', 'zero-downtime', 'deployment', 'app-service', 'staging'],
    description: 'Implement a zero-downtime deployment strategy by using deployment slots. Deploy a new version to staging, test it, and swap it into production seamlessly.'
  },
  {
    id: 'gcp-realtime-analytics-looker',
    title: 'GCP Real-time Analytics with Looker',
    prompt: 'I need a powerful BI tool to create dashboards from my data in BigQuery.',
    level: 'intermediate',
    provider: 'gcp',
    category: 'data',
    services: ['BigQuery', 'Looker', 'IAM'],
    architecture: 'BigQuery + Looker + IAM Service Account',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '30 minutes',
    costLevel: 'high',
    tags: ['bi', 'analytics', 'dashboard', 'real-time', 'visualization'],
    description: 'Connect Google\'s BI platform, Looker, directly to a BigQuery data warehouse to build interactive, real-time dashboards and perform deep-dive data exploration.'
  },
  {
    id: 'aws-cost-anomaly-detection',
    title: 'AWS Cost Anomaly Detection',
    prompt: 'Notify me immediately if my AWS bill suddenly spikes.',
    level: 'beginner',
    provider: 'aws',
    category: 'enterprise',
    services: ['AWS Cost Explorer', 'AWS Cost Anomaly Detection', 'Amazon SNS'],
    architecture: 'Cost Explorer + Cost Anomaly Detection + SNS',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['cost-management', 'monitoring', 'alerts', 'anomaly-detection', 'billing'],
    description: 'Proactively monitor your AWS spending and get alerted automatically when unusual or unexpected cost increases are detected using machine learning.'
  },
  {
    id: 'azure-logic-apps-automation',
    title: 'Azure Logic Apps for Business Process Automation',
    prompt: 'I need a simple way to automatically save all attachments from my invoices email to cloud storage.',
    level: 'beginner',
    provider: 'azure',
    category: 'enterprise',
    services: ['Azure Logic Apps', 'Office 365 Outlook Connector', 'Azure Blob Storage Connector'],
    architecture: 'Logic Apps + Office 365 + Blob Storage',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '20 minutes',
    costLevel: 'low',
    tags: ['automation', 'workflow', 'email', 'blob-storage', 'low-code'],
    description: 'Automate a business workflow using Azure\'s low-code/no-code integration platform to save email attachments from Office 365 directly to Azure Blob Storage.'
  },
  {
    id: 'aws-s3-cross-region-replication',
    title: 'AWS S3 Cross-Region Replication for Disaster Recovery',
    prompt: 'I need to make sure my critical data in S3 survives if an entire AWS region goes down.',
    level: 'intermediate',
    provider: 'aws',
    category: 'enterprise',
    services: ['S3', 'IAM'],
    architecture: 'S3 Primary Bucket + S3 Replica Bucket + IAM Replication Role',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '20 minutes',
    costLevel: 'low',
    tags: ['disaster-recovery', 'replication', 'cross-region', 'backup', 'durability'],
    description: 'Configure Amazon S3 to automatically replicate objects from a bucket in one AWS region to another region to ensure data durability and availability.'
  },
  {
    id: 'azure-sentinel-security',
    title: 'Centralized Logging and Security Analysis with Azure Sentinel',
    prompt: 'I need a single place to see all security alerts and logs from my Azure resources.',
    level: 'intermediate',
    provider: 'azure',
    category: 'security',
    services: ['Azure Sentinel', 'Log Analytics Workspace', 'Data Connectors'],
    architecture: 'Azure Sentinel + Log Analytics + Data Connectors',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '25 minutes',
    costLevel: 'medium',
    tags: ['security', 'siem', 'logging', 'threat-detection', 'analytics'],
    description: 'Set up Azure Sentinel to collect, detect, investigate, and respond to security threats across your Azure environment by ingesting data from various sources.'
  },
  {
    id: 'gcp-batch-hpc-jobs',
    title: 'Running Batch HPC Jobs with Google Cloud Batch',
    prompt: 'I need to run a 10,000-core genomics simulation job overnight without managing my own cluster.',
    level: 'expert',
    provider: 'gcp',
    category: 'enterprise',
    services: ['Cloud Batch', 'Compute Engine', 'Cloud Storage'],
    architecture: 'Cloud Batch + Compute Engine VMs + Cloud Storage',
    exportFormats: ['PNG', 'Terraform'],
    estimatedTime: '30 minutes',
    costLevel: 'high',
    tags: ['hpc', 'batch-processing', 'genomics', 'high-performance', 'auto-scaling'],
    description: 'Define and run large-scale, high-performance computing (HPC) batch jobs on managed infrastructure that automatically provisions and de-provisions resources as needed.'
  },
  {
    id: 'aws-federated-auth-cognito',
    title: 'AWS Federated Authentication with Amazon Cognito',
    prompt: 'I need to add a login system to my app so users can sign in with their Google account.',
    level: 'intermediate',
    provider: 'aws',
    category: 'security',
    services: ['Amazon Cognito', 'IAM', 'S3'],
    architecture: 'Cognito User Pools + Identity Pools + IAM + S3',
    exportFormats: ['Console Tutorial', 'Integration Guide'],
    estimatedTime: '45 minutes',
    costLevel: 'low',
    tags: ['authentication', 'federated', 'social-login', 'cognito', 'oauth'],
    description: 'Add user sign-up, sign-in, and access control to your web application by integrating with Amazon Cognito, allowing users to authenticate with social identity providers.'
  },

  // ADDITIONAL SCENARIOS FROM SCENARION.TXT (Scenarios 19-30)
  {
    id: 'aws-sam-serverless-deployment',
    title: 'Deploying a Serverless Application with AWS SAM CLI',
    prompt: 'Show me the standard AWS-native way to develop and deploy a serverless API.',
    level: 'intermediate',
    provider: 'aws',
    category: 'serverless',
    services: ['API Gateway', 'Lambda', 'DynamoDB', 'SAM CLI'],
    architecture: 'API Gateway + Lambda + DynamoDB + SAM CLI',
    exportFormats: ['Console Tutorial', 'SAM Template'],
    estimatedTime: '30 minutes',
    costLevel: 'low',
    tags: ['serverless', 'sam', 'cli', 'api-gateway', 'lambda'],
    description: 'Learn to build, package, and deploy a complete serverless application using the AWS Serverless Application Model (SAM) command-line interface.'
  },
  {
    id: 'azure-custom-vision-ai',
    title: 'Training a Custom Vision AI Model via the Azure Portal',
    prompt: 'I want to build an AI that can tell the difference between a cat and a dog from a photo.',
    level: 'beginner',
    provider: 'azure',
    category: 'ml-ai',
    services: ['Azure Cognitive Services', 'Custom Vision'],
    architecture: 'Custom Vision + Cognitive Services',
    exportFormats: ['Console Tutorial', 'REST API'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['ai', 'computer-vision', 'machine-learning', 'image-classification'],
    description: 'Use the Azure Custom Vision web portal to upload and tag images, train a machine learning model to recognize specific objects without writing code.'
  },
  {
    id: 'gcp-bigquery-public-datasets',
    title: 'Exploring Public Datasets with BigQuery UI',
    prompt: 'Let me analyze real-world data, like all the Reddit comments or Wikipedia page views.',
    level: 'beginner',
    provider: 'gcp',
    category: 'data',
    services: ['BigQuery', 'BigQuery Public Datasets'],
    architecture: 'BigQuery + Public Datasets',
    exportFormats: ['Console Tutorial', 'SQL Queries'],
    estimatedTime: '20 minutes',
    costLevel: 'low',
    tags: ['bigquery', 'sql', 'public-datasets', 'analytics', 'data-exploration'],
    description: 'Learn to use the Google BigQuery web interface to explore massive public datasets and write standard SQL queries to analyze data.'
  },
  {
    id: 'aws-lake-formation-data-governance',
    title: 'Building a Serverless Data Lake with Lake Formation',
    prompt: 'I need to give my data science team read-only access to specific columns in our customer data, not the whole table.',
    level: 'expert',
    provider: 'aws',
    category: 'data',
    services: ['S3', 'AWS Lake Formation', 'AWS Glue', 'Amazon Athena', 'IAM'],
    architecture: 'S3 + Lake Formation + Glue + Athena + IAM',
    exportFormats: ['Console Tutorial', 'IAM Policies'],
    estimatedTime: '60 minutes',
    costLevel: 'medium',
    tags: ['data-lake', 'governance', 'fine-grained-permissions', 'glue', 'athena'],
    description: 'Build a secure data lake on S3, manage fine-grained permissions for data catalogs and underlying data, and query it using Athena.'
  },
  {
    id: 'azure-front-door-geo-redundant',
    title: 'Deploying a Geo-Redundant Web App with Azure Front Door',
    prompt: 'How do I make my web app fast for users in both the US and Europe, and keep it online if one data center fails?',
    level: 'intermediate',
    provider: 'azure',
    category: 'enterprise',
    services: ['Azure Front Door', 'Azure App Service', 'Resource Groups'],
    architecture: 'Front Door + Multi-Region App Services',
    exportFormats: ['Console Tutorial', 'ARM Template'],
    estimatedTime: '45 minutes',
    costLevel: 'high',
    tags: ['geo-redundancy', 'front-door', 'multi-region', 'failover', 'global'],
    description: 'Build a highly available, global web application by deploying it to two separate Azure regions and using Azure Front Door for routing.'
  },
  {
    id: 'gcp-service-mesh-security',
    title: 'Service-to-Service Authentication with a Service Mesh',
    prompt: 'How can I make sure my \'orders\' microservice only accepts traffic from my \'frontend\' service and nothing else?',
    level: 'expert',
    provider: 'gcp',
    category: 'security',
    services: ['Google Kubernetes Engine (GKE)', 'Anthos Service Mesh', 'Cloud Monitoring'],
    architecture: 'GKE + Anthos Service Mesh + mTLS',
    exportFormats: ['Console Tutorial', 'Kubernetes YAML'],
    estimatedTime: '75 minutes',
    costLevel: 'high',
    tags: ['service-mesh', 'mtls', 'microservices', 'security', 'zero-trust'],
    description: 'Secure microservices communication within a GKE cluster by deploying a service mesh to automatically enforce mutual TLS.'
  },

  // ADDITIONAL SCENARIOS (Scenarios 31-45)
  {
    id: 'aws-waf-web-protection',
    title: 'Protecting a Web Application with AWS WAF',
    prompt: 'How can I protect my website from common hacking attempts?',
    level: 'intermediate',
    provider: 'aws',
    category: 'security',
    services: ['AWS WAF', 'Application Load Balancer', 'EC2 Auto Scaling'],
    architecture: 'WAF + ALB + Auto Scaling Group',
    exportFormats: ['Console Tutorial', 'CloudFormation'],
    estimatedTime: '30 minutes',
    costLevel: 'medium',
    tags: ['waf', 'security', 'web-protection', 'sql-injection', 'xss'],
    description: 'Learn how to secure a public-facing web application against common web exploits and bots using AWS WAF on an Application Load Balancer.'
  },
  {
    id: 'aws-elasticache-database-caching',
    title: 'Database Caching with Amazon ElastiCache',
    prompt: 'My database is overloaded with simple, repetitive read queries. How can I speed things up?',
    level: 'intermediate',
    provider: 'aws',
    category: 'data',
    services: ['Amazon ElastiCache', 'EC2', 'Amazon RDS', 'VPC'],
    architecture: 'ElastiCache Redis + EC2 + RDS + VPC',
    exportFormats: ['Console Tutorial', 'Application Code'],
    estimatedTime: '40 minutes',
    costLevel: 'medium',
    tags: ['caching', 'redis', 'performance', 'database', 'optimization'],
    description: 'Improve your application\'s performance and reduce database load by implementing an in-memory caching layer using Amazon ElastiCache for Redis.'
  },
  {
    id: 'aws-eventbridge-microservices',
    title: 'Decoupling Microservices with Amazon EventBridge',
    prompt: 'When a new order is placed, I need to notify the shipping service and the invoicing service without the order service knowing about them.',
    level: 'intermediate',
    provider: 'aws',
    category: 'serverless',
    services: ['Amazon EventBridge', 'AWS Lambda', 'IAM'],
    architecture: 'EventBridge + Lambda Functions + Event Rules',
    exportFormats: ['Console Tutorial', 'Lambda Code'],
    estimatedTime: '30 minutes',
    costLevel: 'low',
    tags: ['event-driven', 'microservices', 'decoupling', 'serverless', 'messaging'],
    description: 'Build a scalable, event-driven architecture where microservices communicate asynchronously by publishing events to a central event bus.'
  },
  {
    id: 'aws-codepipeline-cicd',
    title: 'Automated CI/CD with AWS CodePipeline',
    prompt: 'I want to set up an automated deployment pipeline for my application on AWS.',
    level: 'expert',
    provider: 'aws',
    category: 'devops',
    services: ['AWS CodePipeline', 'AWS CodeCommit', 'AWS CodeBuild', 'AWS CodeDeploy', 'EC2'],
    architecture: 'CodePipeline + CodeCommit + CodeBuild + CodeDeploy + EC2',
    exportFormats: ['Console Tutorial', 'Pipeline Configuration'],
    estimatedTime: '60 minutes',
    costLevel: 'medium',
    tags: ['cicd', 'pipeline', 'automation', 'deployment', 'devops'],
    description: 'Create a fully automated continuous integration and continuous deployment pipeline that builds, tests, and deploys your application to EC2 instances.'
  },
  {
    id: 'azure-ad-b2c-identity',
    title: 'User Identity Management with Azure AD B2C',
    prompt: 'I need a login page for my app where users can create their own username and password.',
    level: 'intermediate',
    provider: 'azure',
    category: 'security',
    services: ['Azure AD B2C', 'User Flows', 'Custom Policies'],
    architecture: 'Azure AD B2C + User Flows + Custom Branding',
    exportFormats: ['Console Tutorial', 'User Flow Configuration'],
    estimatedTime: '35 minutes',
    costLevel: 'low',
    tags: ['identity', 'authentication', 'b2c', 'user-management', 'social-login'],
    description: 'Provide a secure, customizable, white-label identity management solution for your consumer-facing application.'
  },
  {
    id: 'azure-database-migration-service',
    title: 'Migrating an On-Premises Database with DMS',
    prompt: 'How can I move my company\'s SQL Server database to the cloud without taking it offline for a long time?',
    level: 'expert',
    provider: 'azure',
    category: 'data',
    services: ['Azure Database Migration Service', 'Azure SQL Database', 'SQL Server'],
    architecture: 'DMS + Azure SQL Database + On-Premises SQL Server',
    exportFormats: ['Console Tutorial', 'Migration Guide'],
    estimatedTime: '90 minutes',
    costLevel: 'medium',
    tags: ['migration', 'database', 'minimal-downtime', 'sql-server', 'dms'],
    description: 'Perform a minimal-downtime migration of an on-premises SQL Server database to a fully managed Azure SQL Database using DMS.'
  },

  // ADDITIONAL SCENARIOS (Scenarios 46-60)
  {
    id: 'azure-application-gateway-routing',
    title: 'Path-Based Routing with Application Gateway',
    prompt: 'I want requests for /video to go to my video servers and requests for /images to go to my image servers, all using the same domain name.',
    level: 'intermediate',
    provider: 'azure',
    category: 'enterprise',
    services: ['Application Gateway', 'Virtual Machines', 'Backend Pools'],
    architecture: 'Application Gateway + Backend Pools + Path-based Rules',
    exportFormats: ['Console Tutorial', 'ARM Template'],
    estimatedTime: '40 minutes',
    costLevel: 'medium',
    tags: ['application-gateway', 'path-routing', 'load-balancing', 'backend-pools'],
    description: 'Configure an Application Gateway to route traffic to different backend pools of servers based on the URL path in the user\'s request.'
  },
  {
    id: 'gcp-cloud-armor-ddos',
    title: 'DDoS Protection with Cloud Armor',
    prompt: 'How do I defend my application against large-scale DDoS attacks?',
    level: 'intermediate',
    provider: 'gcp',
    category: 'security',
    services: ['Cloud Armor', 'Cloud Load Balancing', 'Compute Engine'],
    architecture: 'Cloud Armor + Load Balancer + Security Policies',
    exportFormats: ['Console Tutorial', 'Security Policies'],
    estimatedTime: '35 minutes',
    costLevel: 'medium',
    tags: ['ddos-protection', 'cloud-armor', 'security-policies', 'geo-blocking'],
    description: 'Protect your application hosted behind a Google Cloud Load Balancer from DDoS attacks and other web-based threats using Cloud Armor.'
  },
  {
    id: 'gcp-firestore-triggers',
    title: 'Real-time Updates with Firestore Triggers',
    prompt: 'When a user\'s profile document is updated in my database, I need to run some code automatically.',
    level: 'intermediate',
    provider: 'gcp',
    category: 'serverless',
    services: ['Cloud Firestore', 'Cloud Functions', 'Firebase'],
    architecture: 'Firestore + Cloud Functions + Database Triggers',
    exportFormats: ['Console Tutorial', 'Function Code'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['firestore', 'triggers', 'real-time', 'cloud-functions', 'event-driven'],
    description: 'Build an event-driven function that automatically triggers in response to data changes in a Firestore database collection.'
  },
  {
    id: 'gcp-vertex-ai-custom-model',
    title: 'Training a Custom ML Model with Vertex AI',
    prompt: 'I have a CSV file of customer data and I want to train a model to predict which ones are likely to churn.',
    level: 'expert',
    provider: 'gcp',
    category: 'ml-ai',
    services: ['Vertex AI', 'AutoML', 'Cloud Storage', 'BigQuery'],
    architecture: 'Vertex AI + AutoML + Cloud Storage + Model Endpoints',
    exportFormats: ['Console Tutorial', 'Model Deployment'],
    estimatedTime: '90 minutes',
    costLevel: 'high',
    tags: ['vertex-ai', 'automl', 'machine-learning', 'model-training', 'predictions'],
    description: 'Go through the end-to-end machine learning lifecycle by uploading a dataset, training a custom model using AutoML, and deploying it to an endpoint.'
  },
  {
    id: 'aws-elastic-beanstalk-paas',
    title: 'Simplified PaaS Deployment with Elastic Beanstalk',
    prompt: 'I want to deploy my Python web app on AWS, but I don\'t want to deal with setting up servers, load balancers, or auto-scaling myself.',
    level: 'beginner',
    provider: 'aws',
    category: 'web-hosting',
    services: ['AWS Elastic Beanstalk', 'EC2', 'Load Balancer', 'Auto Scaling'],
    architecture: 'Elastic Beanstalk + Managed Infrastructure',
    exportFormats: ['Console Tutorial', 'Application Package'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['paas', 'elastic-beanstalk', 'python', 'managed-deployment', 'auto-scaling'],
    description: 'Learn how to deploy a web application without managing the underlying infrastructure by providing your code to Elastic Beanstalk.'
  },
  {
    id: 'aws-step-functions-workflow',
    title: 'Visual Workflow Orchestration with Step Functions',
    prompt: 'I need to build a reliable process: when an image is uploaded, validate it, then create a thumbnail and extract metadata at the same time, and finally, update a database.',
    level: 'expert',
    provider: 'aws',
    category: 'serverless',
    services: ['AWS Step Functions', 'AWS Lambda', 'S3', 'DynamoDB'],
    architecture: 'Step Functions + Lambda + S3 + DynamoDB',
    exportFormats: ['Console Tutorial', 'State Machine Definition'],
    estimatedTime: '50 minutes',
    costLevel: 'medium',
    tags: ['step-functions', 'workflow', 'orchestration', 'parallel-processing', 'state-machine'],
    description: 'Build a complex, multi-step serverless workflow using a visual designer with parallel processing and error handling.'
  },
  {
    id: 'aws-secrets-manager',
    title: 'Securely Managing Application Secrets with Secrets Manager',
    prompt: 'Where should I store the database password for my application instead of putting it in my source code?',
    level: 'intermediate',
    provider: 'aws',
    category: 'security',
    services: ['AWS Secrets Manager', 'Lambda', 'RDS', 'IAM'],
    architecture: 'Secrets Manager + Lambda + RDS + IAM Roles',
    exportFormats: ['Console Tutorial', 'Application Code'],
    estimatedTime: '30 minutes',
    costLevel: 'low',
    tags: ['secrets-management', 'security', 'credentials', 'iam', 'best-practices'],
    description: 'Learn how to stop hardcoding secrets like database passwords in your application code and retrieve them securely at runtime.'
  },

  // FINAL SCENARIOS (Scenarios 61-65)
  {
    id: 'azure-static-website-blob',
    title: 'Hosting a Static Website with Azure Blob Storage',
    prompt: 'I want to host my simple portfolio website on Azure as cheaply as possible.',
    level: 'beginner',
    provider: 'azure',
    category: 'web-hosting',
    services: ['Azure Blob Storage', 'Static Website Hosting'],
    architecture: 'Blob Storage + Static Website Feature',
    exportFormats: ['Console Tutorial', 'Website Files'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['static-website', 'blob-storage', 'serverless', 'cost-effective', 'portfolio'],
    description: 'Learn how to host a simple, serverless static website using the static website hosting feature of Azure Storage.'
  },
  {
    id: 'azure-container-instances',
    title: 'Running a Simple Container with ACI',
    prompt: 'I have a Docker image. What\'s the fastest way to run it on Azure?',
    level: 'beginner',
    provider: 'azure',
    category: 'containers',
    services: ['Azure Container Instances', 'Container Registry'],
    architecture: 'ACI + Container Image',
    exportFormats: ['Console Tutorial', 'Container Deployment'],
    estimatedTime: '10 minutes',
    costLevel: 'low',
    tags: ['containers', 'aci', 'docker', 'serverless-containers', 'quick-deployment'],
    description: 'Learn how to run a single Docker container quickly and easily without needing to manage any virtual machines or orchestration platforms.'
  },
  {
    id: 'gcp-compute-engine-linux',
    title: 'Launching a Linux VM on Compute Engine',
    prompt: 'I need a basic Linux server on Google Cloud for development.',
    level: 'beginner',
    provider: 'gcp',
    category: 'web-hosting',
    services: ['Compute Engine', 'VPC Network', 'Firewall Rules'],
    architecture: 'Compute Engine VM + VPC + Firewall',
    exportFormats: ['Console Tutorial', 'SSH Access'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['compute-engine', 'linux', 'vm', 'development', 'ssh'],
    description: 'Learn the basics of Google Cloud\'s IaaS by launching a Debian Linux virtual machine and connecting to it securely using the browser-based SSH client.'
  },
  {
    id: 'gcp-cloud-sql-database',
    title: 'Creating a Cloud SQL Database',
    prompt: 'I need a managed MySQL database on GCP for my application.',
    level: 'beginner',
    provider: 'gcp',
    category: 'data',
    services: ['Cloud SQL', 'MySQL', 'VPC Network'],
    architecture: 'Cloud SQL + MySQL + Network Security',
    exportFormats: ['Console Tutorial', 'Database Connection'],
    estimatedTime: '20 minutes',
    costLevel: 'medium',
    tags: ['cloud-sql', 'mysql', 'managed-database', 'relational', 'connection'],
    description: 'Learn how to create and connect to a fully managed MySQL database using Cloud SQL, Google Cloud\'s managed relational database service.'
  },
  {
    id: 'gcp-cloud-storage-sharing',
    title: 'Storing and Sharing Files with Cloud Storage',
    prompt: 'I need to store private user documents and occasionally generate a secure, temporary download link for them.',
    level: 'beginner',
    provider: 'gcp',
    category: 'data',
    services: ['Cloud Storage', 'IAM', 'Signed URLs'],
    architecture: 'Cloud Storage + IAM + Signed URLs',
    exportFormats: ['Console Tutorial', 'Signed URL Generation'],
    estimatedTime: '25 minutes',
    costLevel: 'low',
    tags: ['cloud-storage', 'file-sharing', 'signed-urls', 'security', 'object-storage'],
    description: 'Learn the basics of Google Cloud Storage by creating a bucket, uploading objects, and securely sharing an object with signed URLs.'
  },

  // MISSING SCENARIOS TO COMPLETE 65 SCENARIOS
  {
    id: 'aws-kinesis-stream-analytics',
    title: 'Real-time Stream Analytics with Kinesis Data Analytics',
    prompt: 'I need to analyze sensor data in real-time and alert me if the temperature goes over a certain average for 1 minute.',
    level: 'expert',
    provider: 'aws',
    category: 'data',
    services: ['Kinesis Data Streams', 'Kinesis Data Analytics', 'Lambda', 'SNS'],
    architecture: 'Kinesis Data Streams + Analytics + Lambda + SNS',
    exportFormats: ['Console Tutorial', 'SQL Queries'],
    estimatedTime: '50 minutes',
    costLevel: 'medium',
    tags: ['real-time-analytics', 'kinesis', 'stream-processing', 'sql', 'alerts'],
    description: 'Learn to run continuous SQL queries against a real-time data stream to perform time-series analysis, detect anomalies, and generate alerts.'
  },
  {
    id: 'aws-simple-ec2-webserver',
    title: 'Launching a Simple EC2 Web Server',
    prompt: 'I need a simple virtual server in the cloud to host a test website.',
    level: 'beginner',
    provider: 'aws',
    category: 'web-hosting',
    services: ['EC2', 'Security Groups', 'Elastic IP'],
    architecture: 'EC2 + Security Groups + Web Server',
    exportFormats: ['Console Tutorial', 'SSH Access'],
    estimatedTime: '20 minutes',
    costLevel: 'low',
    tags: ['ec2', 'web-server', 'linux', 'apache', 'basic-hosting'],
    description: 'Learn the most fundamental IaaS concept by launching a single Linux virtual machine, installing a basic web server, and making it accessible from the internet.'
  },
  {
    id: 'aws-s3-file-storage',
    title: 'Storing Files in Amazon S3',
    prompt: 'I need a place to store my application\'s user-uploaded images.',
    level: 'beginner',
    provider: 'aws',
    category: 'data',
    services: ['S3', 'IAM', 'Bucket Policies'],
    architecture: 'S3 Bucket + IAM Policies + Access Control',
    exportFormats: ['Console Tutorial', 'Bucket Configuration'],
    estimatedTime: '15 minutes',
    costLevel: 'low',
    tags: ['s3', 'object-storage', 'file-upload', 'permissions', 'bucket-policy'],
    description: 'Learn the basics of object storage by creating an S3 bucket and uploading files to it. Understand the difference between private and public objects.'
  }
];

export const getScenariosByLevel = (level: 'beginner' | 'intermediate' | 'expert') => {
  return cloudScenarios.filter(scenario => scenario.level === level);
};

export const getScenariosByProvider = (provider: 'aws' | 'azure' | 'gcp' | 'multi-cloud' | 'hybrid') => {
  return cloudScenarios.filter(scenario => scenario.provider === provider);
};

export const getScenariosByCategory = (category: string) => {
  return cloudScenarios.filter(scenario => scenario.category === category);
};

export const searchScenarios = (query: string) => {
  const lowercaseQuery = query.toLowerCase();
  return cloudScenarios.filter(scenario => 
    scenario.title.toLowerCase().includes(lowercaseQuery) ||
    scenario.description.toLowerCase().includes(lowercaseQuery) ||
    scenario.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    scenario.services.some(service => service.toLowerCase().includes(lowercaseQuery))
  );
};
