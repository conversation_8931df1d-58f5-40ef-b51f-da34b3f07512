{"name": "samwi-learn", "private": true, "version": "1.0.0", "type": "module", "workspaces": ["apps/*", "modules/*", "shared/*"], "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start:web": "npm run dev --workspace=apps/web-frontend", "start:k8s": "npm run dev --workspace=modules/kubernetes-course", "start:cloud": "npm run dev --workspace=modules/cloud-architecture", "build:all": "npm run build --workspaces", "install:all": "npm install --workspaces"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "js-yaml": "^4.1.0", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^7.6.3", "react-helmet-async": "^1.3.0", "react-intersection-observer": "^9.4.3", "reactflow": "^11.10.4", "tailwind-merge": "^3.3.1", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/js-yaml": "^4.0.9", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}