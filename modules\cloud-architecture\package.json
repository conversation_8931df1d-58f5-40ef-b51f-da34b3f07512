{"name": "cloud-architecture", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.6.3", "framer-motion": "^10.16.16", "lucide-react": "^0.344.0", "@monaco-editor/react": "^4.6.0", "monaco-editor": "^0.45.0", "clsx": "^2.1.1", "tailwind-merge": "^3.3.1", "react-hot-toast": "^2.4.1", "zustand": "^4.5.0", "date-fns": "^2.30.0", "react-helmet-async": "^1.3.0", "react-intersection-observer": "^9.4.3", "shared-ui": "*", "shared-utils": "*"}, "devDependencies": {"@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "vite": "^5.4.2", "typescript": "^5.5.3", "eslint": "^9.9.1", "tailwindcss": "^3.4.1", "autoprefixer": "^10.4.18", "postcss": "^8.4.35"}}